#pragma once

#include "Hooks/Hook.h"

// Function type definition.
typedef void(__fastcall* ProcessRemoteFunctionType)(void* Driver, CG::Engine::AActor* Actor,
                                                    CG::CoreUObject::UFunction* Function, void* Parameters, void* OutParms, void* Stack, CG::CoreUObject::UObject* SubObject);

// Our specialized hook class.
class ProcessRemoteFunctionHook : public Hook<ProcessRemoteFunctionType>
{
public:
    ProcessRemoteFunctionHook();

    // This is our hook callback.
    static void __fastcall HookProcessRemoteFunction(void* Driver, CG::Engine::AActor* Actor,
                                                     CG::CoreUObject::UFunction* Function, void* Parameters, void* OutParms, void* Stack, CG::CoreUObject::UObject* SubObject);

    // Used to allow the static hook callback to call the stored original function.
    static ProcessRemoteFunctionHook* Instance;
};

// Public interface functions.
bool SetupProcessRemoteFunctionHook();
void RemoveProcessRemoteFunctionHook();
