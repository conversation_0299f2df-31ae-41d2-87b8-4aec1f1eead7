#pragma once
#include "Hooks/Hook.h"

// Function type definition
typedef void(__fastcall* GetPlayerViewPointType)(
    CG::Engine::AController* thiz,
    CG::CoreUObject::FVector* out_Location,
    CG::CoreUObject::FRotator* out_Rotation);

// Our specialized hook class
class GetPlayerViewPointHook : public Hook<GetPlayerViewPointType>
{
public:
    GetPlayerViewPointHook();

    // This is our hook callback
    static void __fastcall HookGetPlayerViewPoint(
        CG::Engine::AController* thiz,
        CG::CoreUObject::FVector* out_Location,
        CG::CoreUObject::FRotator* out_Rotation);

    // Used to allow the static hook callback to call the stored original function
    static GetPlayerViewPointHook* Instance;
};

// Public interface functions
bool SetupGetPlayerViewPointHook();
void RemoveGetPlayerViewPointHook();