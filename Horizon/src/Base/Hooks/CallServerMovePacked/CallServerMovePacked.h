#pragma once
#include "Hooks/Hook.h"

// Function type definition
typedef void(__fastcall* CallServerMovePackedType)(
    CG::Engine::UCharacterMovementComponent* thiz,
    const FSavedMove_Character* NewMove,
    const FSavedMove_Character* PendingMove,
    const FSavedMove_Character* OldMove);

// Our specialized hook class
class CallServerMovePackedHook : public Hook<CallServerMovePackedType>
{
public:
    CallServerMovePackedHook();

    // This is our hook callback
    static void __fastcall HookCallServerMovePacked(
        CG::Engine::UCharacterMovementComponent* thiz,
        const FSavedMove_Character* NewMove,
        const FSavedMove_Character* PendingMove,
        const FSavedMove_Character* OldMove);

    // Used to allow the static hook callback to call the stored original function
    static CallServerMovePackedHook* Instance;
};

// Public interface functions
bool SetupCallServerMovePackedHook();
void RemoveCallServerMovePackedHook();