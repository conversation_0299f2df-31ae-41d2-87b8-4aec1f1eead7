#pragma once

#include "Hooks/Hook.h"

// Function type definition.
typedef void(__thiscall* PostRenderType)(CG::ShooterGame::UShooterGameViewportClient* thiz, CG::Engine::UCanvas* canvas);

// Our specialized hook class.
class PostRenderHook : public Hook<PostRenderType>
{
public:
    PostRenderHook();

    // This is our hook callback.
    static void __stdcall HookPostRender(CG::ShooterGame::UShooterGameViewportClient* thiz, CG::Engine::UCanvas* canvas);

    // Allows the static hook callback to call the stored original function.
    static PostRenderHook* Instance;
};

// Public interface functions.
bool SetupPostRenderHook();
void RemovePostRenderHook();
