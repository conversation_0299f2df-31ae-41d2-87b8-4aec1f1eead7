#include "pch.h"
#include "PostRender.h"

#include "ModuleSystem/ModuleBase/Settings/Config/Config.h"
#include "ModuleSystem/ModuleManager/ModuleManager.h"

// The signature and mask for scanning.
static const char* Signature = "\x40\x55\x56\x57\x48\x8D\x6C\x24\x00\x48\x81\xEC\xA0\x00\x00\x00\x45\x33\xC0";
static const char* Mask = "xxxxxxxx?xxxxxxxxxx";

// Define the instance pointer.
PostRenderHook* PostRenderHook::Instance = nullptr;

PostRenderHook::PostRenderHook()
	: Hook<PostRenderType>(Signature, Mask, &PostRenderHook::HookPostRender)
{
	Instance = this;
}

static auto LastReadTime = std::chrono::steady_clock::now();


// The actual hook function.
void __stdcall PostRenderHook::HookPostRender(CG::ShooterGame::UShooterGameViewportClient* thiz, CG::Engine::UCanvas* canvas)
{
	static auto* font = CG::CoreUObject::UObject::FindObject<CG::Engine::UFont>(XOR("Font /Game/OldAssets/UI/HUD/SansationBold18.SansationBold18"));
	if (!font)
	{
		printf("Font not found!\n");
	}

	if (!thiz) return;

	auto world = thiz->World;

	if (world)
	{
		if (world->GameState && world->GameState->IsA(CG::ShooterGame::AShooterGameState::StaticClass()))
		{
			auto shooterGameState = static_cast<CG::ShooterGame::AShooterGameState*>(world->GameState);

			Global::ServerFps = static_cast<int>(shooterGameState->ServerFramerate);
		}
		else
		{
			/* Not on server */
			/*for (Base::Module* module : Base::Manager.GetModules())
			{
				if (module->GetName() == XOR("Player Disconnecter"))
				{
					((Base::ModulePlayerDisconnecter*)module)->whistleFunction = nullptr;
				}
			}*/
		}

		if (auto playerController = Global::GetPlayerController(world))
		{
			playerController->GetViewportSize(&Global::ScreenWidth, &Global::ScreenHeight);
		}
	}

	if (std::chrono::steady_clock::now() - LastReadTime > std::chrono::milliseconds(4000))
	{
		// Load config.json from disk
		std::ifstream configFile(Base::GetConfigPath());
		if (!configFile.is_open())
		{
#ifdef DEBUG
			std::cerr << XOR("Failed to open config file") << std::endl;
#endif
		}

		nlohmann::json config;
		configFile >> config;
		configFile.close();

		if (Base::CheckAndResetChangedFlag(config) || !Global::InitialLoad)
		{
			for (Base::Module* module : Base::Manager.GetModules())
			{
				if (Global::IsConfigAccessible())
				{
					try
					{
						module->LoadSettings(config);
					}
					catch (const nlohmann::json::parse_error& e)
					{
#ifdef DEBUG
						std::cerr << XOR("Parse error: ") << e.what() << std::endl;
#endif
					}
				}
			}

			Global::InitialLoad = true;
		}

		LastReadTime = std::chrono::steady_clock::now();
	}

	for (Base::Module* module : Base::Manager.GetModules())
	{
		if (GetAsyncKeyState(module->GetKeyCode()) & 0x8000 && module->GetKeyCode() != 0x00) // if (Global::_GetAsyncKeyState(module->GetKeyCode()) & 0x8000)
		{
			if (!module->IsToggling())
			{
				module->SetToggling(true);
				module->Toggle();
			}
		}
		else
		{
			module->SetToggling(false);
		}

		if (module->GetEnabled())
		{
			if (world)
			{
				module->OnPostRender(thiz, canvas, world, font);
			}
		}
	}



	// Call the original function.
	if (Instance && Instance->GetOriginal())
	{
		Instance->GetOriginal()(thiz, canvas);
	}
}

// Public interface functions.
bool SetupPostRenderHook()
{
	auto hook = new PostRenderHook();
	return hook->Setup();
}

void RemovePostRenderHook()
{
	if (PostRenderHook::Instance)
		PostRenderHook::Instance->Remove();
}
