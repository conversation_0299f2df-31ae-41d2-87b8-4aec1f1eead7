// dllmain.cpp : Defines the entry point for the DLL application.
#include "pch.h"

#include <ModuleSystem/ModuleManager/ModuleManager.h>

#include "Hooks/HookManager/HookManager.h"

BOOL APIENTRY DllMain( HMODULE hModule,
                       DWORD  ul_reason_for_call,
                       LPVOID lpReserved
)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
        AllocConsole();
        freopen_s(&Global::File, XOR("CONOUT$"), XOR("w"), stdout);
        printf(XOR("Initializing\n"));
        Base::ModuleManager::Initialize();
        CG::BasicTypes::InitSdk();
        Base::GenerateConfig();
        //SignatureScanner::UnlockDLCs();
        Base::FNameCache::Initialize();
        InitializeHooks();
    case DLL_THREAD_ATTACH:
    case DLL_THREAD_DETACH:
    case DLL_PROCESS_DETACH:
        break;
    }
    return TRUE;
}

