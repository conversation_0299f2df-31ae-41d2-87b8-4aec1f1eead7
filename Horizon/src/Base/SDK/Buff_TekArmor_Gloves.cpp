/**
 * ----------------------------------------
 * |        Generated By CheatGear        |
 * ----------------------------------------
 * | Game:    ArkAscended                 |
 * | Version: 06.06.2025                  |
 * | Date:    06/06/2025                  |
 * ----------------------------------------
 */

#include "pch.h"
#include <cstdint>
#include <vector>
#include <string>
#include <locale>
#include <unordered_set>
#include "Headers/Global_DEFINES.h"
#include "Headers/BasicTypes.h"
#include "Headers/CoreUObject_UFunction.h"
#include "Headers/Buff_TekArmor_Gloves_PARAMS.h"
#include "Headers/Buff_TekArmor_Gloves_ABuff_TekArmor_Gloves_C.h"

#ifdef _MSC_VER
    #pragma pack(push, 0x01)
#endif

namespace CG::Buff_<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>loves
{
    // --------------------------------------------------
    // # Structs functions
    // --------------------------------------------------
    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.BlockTekTeleporter
     *         Flags  -> (Public, HasOutParms, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         bool                                               PreventTeleport                                            (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::BlockTekTeleporter(bool* PreventTeleport)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.BlockTekTeleporter"));
        
        ABuff_TekArmor_Gloves_C_BlockTekTeleporter_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        if (PreventTeleport != nullptr)
            *PreventTeleport = params.PreventTeleport;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.Set VFX Color
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::SetVFXColor()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.Set VFX Color"));
        
        ABuff_TekArmor_Gloves_C_SetVFXColor_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.SetParticleState
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         bool                                               bIsFPVParticle                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         bool                                               bIsParticleSystemComponent                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         bool                                               bSetActive                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         bool                                               bReset                                                     (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::SetParticleState(bool bIsFPVParticle, bool bIsParticleSystemComponent, bool bSetActive, bool bReset)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.SetParticleState"));
        
        ABuff_TekArmor_Gloves_C_SetParticleState_Params params {};
        params.bIsFPVParticle = bIsFPVParticle;
        params.bIsParticleSystemComponent = bIsParticleSystemComponent;
        params.bSetActive = bSetActive;
        params.bReset = bReset;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.Validate Punch State
     *         Flags  -> (Public, HasOutParms, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         E_TekGlovePunchState::E_TekGlovePunchState         NewState                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         bool                                               bValidState                                                (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::ValidatePunchState(E_TekGlovePunchState::E_TekGlovePunchState NewState, bool* bValidState)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.Validate Punch State"));
        
        ABuff_TekArmor_Gloves_C_ValidatePunchState_Params params {};
        params.NewState = NewState;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        if (bValidState != nullptr)
            *bValidState = params.bValidState;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.PreventBlockingWithShield
     *         Flags  -> (Event, Public, HasOutParms, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         bool                                               ReturnValue                                                (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     */
    bool ABuff_TekArmor_Gloves_C::PreventBlockingWithShield()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.PreventBlockingWithShield"));
        
        ABuff_TekArmor_Gloves_C_PreventBlockingWithShield_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        return params.ReturnValue;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.HasShield
     *         Flags  -> (Public, HasOutParms, BlueprintCallable, BlueprintEvent, BlueprintPure)
     * Parameters:
     *         bool                                               ReturnValue                                                (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     */
    bool ABuff_TekArmor_Gloves_C::HasShield()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.HasShield"));
        
        ABuff_TekArmor_Gloves_C_HasShield_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        return params.ReturnValue;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.ShieldActive
     *         Flags  -> (Public, HasOutParms, BlueprintCallable, BlueprintEvent, BlueprintPure)
     * Parameters:
     *         bool                                               ReturnValue                                                (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     */
    bool ABuff_TekArmor_Gloves_C::ShieldActive()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.ShieldActive"));
        
        ABuff_TekArmor_Gloves_C_ShieldActive_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        return params.ReturnValue;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.BPCheckPreventInput
     *         Flags  -> (Event, Public, HasOutParms, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         ShooterGame::EPrimalCharacterInputType             inputType                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         bool                                               ReturnValue                                                (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     */
    bool ABuff_TekArmor_Gloves_C::BPCheckPreventInput(ShooterGame::EPrimalCharacterInputType inputType)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.BPCheckPreventInput"));
        
        ABuff_TekArmor_Gloves_C_BPCheckPreventInput_Params params {};
        params.inputType = inputType;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        return params.ReturnValue;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.CanPhysicallyTekPunch
     *         Flags  -> (Public, HasOutParms, BlueprintCallable, BlueprintEvent, BlueprintPure)
     * Parameters:
     *         bool                                               Result                                                     (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::CanPhysicallyTekPunch(bool* Result)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.CanPhysicallyTekPunch"));
        
        ABuff_TekArmor_Gloves_C_CanPhysicallyTekPunch_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        if (Result != nullptr)
            *Result = params.Result;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.Should Damage Actor
     *         Flags  -> (Public, HasOutParms, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         Engine::AActor*                                    Victim                                                     (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
     *         bool                                               Result                                                     (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::ShouldDamageActor(Engine::AActor* Victim, bool* Result)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.Should Damage Actor"));
        
        ABuff_TekArmor_Gloves_C_ShouldDamageActor_Params params {};
        params.Victim = Victim;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        if (Result != nullptr)
            *Result = params.Result;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.Has Line Of SightToActor
     *         Flags  -> (Public, HasOutParms, HasDefaults, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         Engine::AActor*                                    Actor                                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
     *         bool                                               Result                                                     (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::HasLineOfSightToActor(Engine::AActor* Actor, bool* Result)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.Has Line Of SightToActor"));
        
        ABuff_TekArmor_Gloves_C_HasLineOfSightToActor_Params params {};
        params.Actor = Actor;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        if (Result != nullptr)
            *Result = params.Result;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.ArePunchChargeAnimsPlaying
     *         Flags  -> (Public, HasOutParms, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         bool                                               cleanUpAnims                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         bool                                               Result                                                     (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::ArePunchChargeAnimsPlaying(bool cleanUpAnims, bool* Result)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.ArePunchChargeAnimsPlaying"));
        
        ABuff_TekArmor_Gloves_C_ArePunchChargeAnimsPlaying_Params params {};
        params.cleanUpAnims = cleanUpAnims;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        if (Result != nullptr)
            *Result = params.Result;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.PlayPunchAnimsByState
     *         Flags  -> (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         E_TekGlovePunchState::E_TekGlovePunchState         State                                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::PlayPunchAnimsByState(E_TekGlovePunchState::E_TekGlovePunchState State)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.PlayPunchAnimsByState"));
        
        ABuff_TekArmor_Gloves_C_PlayPunchAnimsByState_Params params {};
        params.State = State;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.IsPlayerAboveGround
     *         Flags  -> (Public, HasOutParms, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         bool                                               Result                                                     (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::IsPlayerAboveGround(bool* Result)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.IsPlayerAboveGround"));
        
        ABuff_TekArmor_Gloves_C_IsPlayerAboveGround_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        if (Result != nullptr)
            *Result = params.Result;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.AreTekPantsEquipped
     *         Flags  -> (Public, HasOutParms, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         bool                                               Result                                                     (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         Buff_TekArmor_Pants::ABuff_TekArmor_Pants_C*       tekPantsRef                                                (Parm, OutParm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::AreTekPantsEquipped(bool* Result, Buff_TekArmor_Pants::ABuff_TekArmor_Pants_C** tekPantsRef)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.AreTekPantsEquipped"));
        
        ABuff_TekArmor_Gloves_C_AreTekPantsEquipped_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        if (Result != nullptr)
            *Result = params.Result;
        if (tekPantsRef != nullptr)
            *tekPantsRef = params.tekPantsRef;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.GetBuffPostprocessIntensity
     *         Flags  -> (Event, Public, HasOutParms, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         float                                              ReturnValue                                                (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     */
    float ABuff_TekArmor_Gloves_C::GetBuffPostprocessIntensity()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.GetBuffPostprocessIntensity"));
        
        ABuff_TekArmor_Gloves_C_GetBuffPostprocessIntensity_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        return params.ReturnValue;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.BPDeactivated
     *         Flags  -> (Event, Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         Engine::AActor*                                    ForInstigator                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::BPDeactivated(Engine::AActor* ForInstigator)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.BPDeactivated"));
        
        ABuff_TekArmor_Gloves_C_BPDeactivated_Params params {};
        params.ForInstigator = ForInstigator;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.Update Local Vars by State
     *         Flags  -> (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         E_TekGlovePunchState::E_TekGlovePunchState         NewState                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::UpdateLocalVarsbyState(E_TekGlovePunchState::E_TekGlovePunchState NewState)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.Update Local Vars by State"));
        
        ABuff_TekArmor_Gloves_C_UpdateLocalVarsbyState_Params params {};
        params.NewState = NewState;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.GetPlayerCameraLocation
     *         Flags  -> (Public, HasOutParms, BlueprintCallable, BlueprintEvent, BlueprintPure)
     * Parameters:
     *         CoreUObject::FVector                               Location                                                   (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::GetPlayerCameraLocation(CoreUObject::FVector* Location)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.GetPlayerCameraLocation"));
        
        ABuff_TekArmor_Gloves_C_GetPlayerCameraLocation_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        if (Location != nullptr)
            *Location = params.Location;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.BPSetupForInstigator
     *         Flags  -> (Event, Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         Engine::AActor*                                    ForInstigator                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::BPSetupForInstigator(Engine::AActor* ForInstigator)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.BPSetupForInstigator"));
        
        ABuff_TekArmor_Gloves_C_BPSetupForInstigator_Params params {};
        params.ForInstigator = ForInstigator;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.RocketPunchEnd
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::RocketPunchEnd()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.RocketPunchEnd"));
        
        ABuff_TekArmor_Gloves_C_RocketPunchEnd_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.ResetOwningPlayerVariables
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::ResetOwningPlayerVariables()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.ResetOwningPlayerVariables"));
        
        ABuff_TekArmor_Gloves_C_ResetOwningPlayerVariables_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.PreventJump
     *         Flags  -> (Event, Public, HasOutParms, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         bool                                               ReturnValue                                                (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     */
    bool ABuff_TekArmor_Gloves_C::PreventJump()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.PreventJump"));
        
        ABuff_TekArmor_Gloves_C_PreventJump_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        return params.ReturnValue;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.NetSetTekPunchState
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         E_TekGlovePunchState::E_TekGlovePunchState         newPunchState                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::NetSetTekPunchState(E_TekGlovePunchState::E_TekGlovePunchState newPunchState)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.NetSetTekPunchState"));
        
        ABuff_TekArmor_Gloves_C_NetSetTekPunchState_Params params {};
        params.newPunchState = newPunchState;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.BuffTickServer
     *         Flags  -> (Event, Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         float                                              DeltaTime                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::BuffTickServer(float DeltaTime)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.BuffTickServer"));
        
        ABuff_TekArmor_Gloves_C_BuffTickServer_Params params {};
        params.DeltaTime = DeltaTime;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.Client_SetTekPunchState
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         E_TekGlovePunchState::E_TekGlovePunchState         NewState                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::Client_SetTekPunchState(E_TekGlovePunchState::E_TekGlovePunchState NewState)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.Client_SetTekPunchState"));
        
        ABuff_TekArmor_Gloves_C_Client_SetTekPunchState_Params params {};
        params.NewState = NewState;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.GetTimeToRocketPunchTarget
     *         Flags  -> (Public, HasOutParms, BlueprintCallable, BlueprintEvent, BlueprintPure)
     * Parameters:
     *         CoreUObject::FVector                               TargetLocation                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         double                                             ReturnValue                                                (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     */
    double ABuff_TekArmor_Gloves_C::GetTimeToRocketPunchTarget(const CoreUObject::FVector& TargetLocation)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.GetTimeToRocketPunchTarget"));
        
        ABuff_TekArmor_Gloves_C_GetTimeToRocketPunchTarget_Params params {};
        params.TargetLocation = TargetLocation;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        return params.ReturnValue;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.CheckForTargetsWhileRocketPunching
     *         Flags  -> (Public, HasOutParms, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         bool                                               punchNow                                                   (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::CheckForTargetsWhileRocketPunching(bool* punchNow)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.CheckForTargetsWhileRocketPunching"));
        
        ABuff_TekArmor_Gloves_C_CheckForTargetsWhileRocketPunching_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        if (punchNow != nullptr)
            *punchNow = params.punchNow;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.Set AudioParams and Play
     *         Flags  -> (Public, HasOutParms, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         Engine::UAudioComponent*                           Audio                                                      (BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ZeroConstructor, InstancedReference, ReferenceParm, NoDestructor, HasGetValueTypeHash)
     *         double                                             Volume                                                     (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         double                                             Pitch                                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         bool                                               PlaySound                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::SetAudioParamsandPlay(Engine::UAudioComponent** Audio, double Volume, double Pitch, bool PlaySound)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.Set AudioParams and Play"));
        
        ABuff_TekArmor_Gloves_C_SetAudioParamsandPlay_Params params {};
        params.Volume = Volume;
        params.Pitch = Pitch;
        params.PlaySound = PlaySound;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        if (Audio != nullptr)
            *Audio = params.Audio;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.OnRep_CurrentPunchState
     *         Flags  -> (BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::OnRep_CurrentPunchState()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.OnRep_CurrentPunchState"));
        
        ABuff_TekArmor_Gloves_C_OnRep_CurrentPunchState_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.GetRelativeCenterOfMass
     *         Flags  -> (Public, HasOutParms, BlueprintCallable, BlueprintEvent, BlueprintPure)
     * Parameters:
     *         ShooterGame::APrimalCharacter*                     Character                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
     *         CoreUObject::FVector                               ReturnValue                                                (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     */
    CoreUObject::FVector ABuff_TekArmor_Gloves_C::GetRelativeCenterOfMass(ShooterGame::APrimalCharacter* Character)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.GetRelativeCenterOfMass"));
        
        ABuff_TekArmor_Gloves_C_GetRelativeCenterOfMass_Params params {};
        params.Character = Character;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        return params.ReturnValue;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.DrawBuffFloatingHUD
     *         Flags  -> (Event, Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         int32_t                                            BuffIndex                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         ShooterGame::AShooterHUD*                          HUD                                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
     *         float                                              CenterX                                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         float                                              CenterY                                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         float                                              DrawScale                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::DrawBuffFloatingHUD(int32_t BuffIndex, ShooterGame::AShooterHUD* HUD, float CenterX, float CenterY, float DrawScale)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.DrawBuffFloatingHUD"));
        
        ABuff_TekArmor_Gloves_C_DrawBuffFloatingHUD_Params params {};
        params.BuffIndex = BuffIndex;
        params.HUD = HUD;
        params.CenterX = CenterX;
        params.CenterY = CenterY;
        params.DrawScale = DrawScale;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.ScanForRocketFistTargets
     *         Flags  -> (Public, HasOutParms, HasDefaults, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         double                                             Range                                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         bool                                               anyHit                                                     (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         CoreUObject::FVector                               HitLocation                                                (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         ShooterGame::APrimalCharacter*                     hitPawn                                                    (Parm, OutParm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::ScanForRocketFistTargets(double Range, bool* anyHit, CoreUObject::FVector* HitLocation, ShooterGame::APrimalCharacter** hitPawn)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.ScanForRocketFistTargets"));
        
        ABuff_TekArmor_Gloves_C_ScanForRocketFistTargets_Params params {};
        params.Range = Range;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        if (anyHit != nullptr)
            *anyHit = params.anyHit;
        if (HitLocation != nullptr)
            *HitLocation = params.HitLocation;
        if (hitPawn != nullptr)
            *hitPawn = params.hitPawn;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.AccurateSphereCheck
     *         Flags  -> (Public, HasOutParms, HasDefaults, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         CoreUObject::FVector                               EndLocation                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         double                                             SphereRadius                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         bool                                               IgnoreFriendlies                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         bool                                               HitPawnOrStructure                                         (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::AccurateSphereCheck(const CoreUObject::FVector& EndLocation, double SphereRadius, bool IgnoreFriendlies, bool* HitPawnOrStructure)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.AccurateSphereCheck"));
        
        ABuff_TekArmor_Gloves_C_AccurateSphereCheck_Params params {};
        params.EndLocation = EndLocation;
        params.SphereRadius = SphereRadius;
        params.IgnoreFriendlies = IgnoreFriendlies;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        if (HitPawnOrStructure != nullptr)
            *HitPawnOrStructure = params.HitPawnOrStructure;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.Spawn Sound at Fist Location
     *         Flags  -> (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         Engine::USoundBase*                                SoundToSpawn                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
     *         double                                             VolumeMultiplier                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         double                                             PitchMultiplier                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::SpawnSoundatFistLocation(Engine::USoundBase* SoundToSpawn, double VolumeMultiplier, double PitchMultiplier)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.Spawn Sound at Fist Location"));
        
        ABuff_TekArmor_Gloves_C_SpawnSoundatFistLocation_Params params {};
        params.SoundToSpawn = SoundToSpawn;
        params.VolumeMultiplier = VolumeMultiplier;
        params.PitchMultiplier = PitchMultiplier;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.BuffTickClient
     *         Flags  -> (Event, Public, HasDefaults, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         float                                              DeltaTime                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::BuffTickClient(float DeltaTime)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.BuffTickClient"));
        
        ABuff_TekArmor_Gloves_C_BuffTickClient_Params params {};
        params.DeltaTime = DeltaTime;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.Get Punch Location
     *         Flags  -> (Public, HasOutParms, BlueprintCallable, BlueprintEvent, BlueprintPure)
     * Parameters:
     *         CoreUObject::FVector                               Location                                                   (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::GetPunchLocation(CoreUObject::FVector* Location)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.Get Punch Location"));
        
        ABuff_TekArmor_Gloves_C_GetPunchLocation_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        if (Location != nullptr)
            *Location = params.Location;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.RocketPunchStart
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::RocketPunchStart()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.RocketPunchStart"));
        
        ABuff_TekArmor_Gloves_C_RocketPunchStart_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.Punch
     *         Flags  -> (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::Punch()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.Punch"));
        
        ABuff_TekArmor_Gloves_C_Punch_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.CanUseTekAbility
     *         Flags  -> (Public, HasOutParms, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         bool                                               bNotifyIfOutOfElement                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         bool                                               bResult                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::CanUseTekAbility(bool bNotifyIfOutOfElement, bool* bResult)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.CanUseTekAbility"));
        
        ABuff_TekArmor_Gloves_C_CanUseTekAbility_Params params {};
        params.bNotifyIfOutOfElement = bNotifyIfOutOfElement;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        if (bResult != nullptr)
            *bResult = params.bResult;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.SetCastedArmorPieceRef
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::SetCastedArmorPieceRef()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.SetCastedArmorPieceRef"));
        
        ABuff_TekArmor_Gloves_C_SetCastedArmorPieceRef_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.UserConstructionScript
     *         Flags  -> (Event, Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::UserConstructionScript()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.UserConstructionScript"));
        
        ABuff_TekArmor_Gloves_C_UserConstructionScript_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.InpActEvt_AltFire_K2Node_InputActionEvent_6
     *         Flags  -> (BlueprintEvent)
     * Parameters:
     *         InputCore::FKey                                    Key                                                        (BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::InpActEvt_AltFire_K2Node_InputActionEvent_6(const InputCore::FKey& Key)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.InpActEvt_AltFire_K2Node_InputActionEvent_6"));
        
        ABuff_TekArmor_Gloves_C_InpActEvt_AltFire_K2Node_InputActionEvent_6_Params params {};
        params.Key = Key;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.InpActEvt_AltFire_K2Node_InputActionEvent_5
     *         Flags  -> (BlueprintEvent)
     * Parameters:
     *         InputCore::FKey                                    Key                                                        (BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::InpActEvt_AltFire_K2Node_InputActionEvent_5(const InputCore::FKey& Key)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.InpActEvt_AltFire_K2Node_InputActionEvent_5"));
        
        ABuff_TekArmor_Gloves_C_InpActEvt_AltFire_K2Node_InputActionEvent_5_Params params {};
        params.Key = Key;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.InpActEvt_Fire_K2Node_InputActionEvent_4
     *         Flags  -> (BlueprintEvent)
     * Parameters:
     *         InputCore::FKey                                    Key                                                        (BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::InpActEvt_Fire_K2Node_InputActionEvent_4(const InputCore::FKey& Key)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.InpActEvt_Fire_K2Node_InputActionEvent_4"));
        
        ABuff_TekArmor_Gloves_C_InpActEvt_Fire_K2Node_InputActionEvent_4_Params params {};
        params.Key = Key;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.InpActEvt_Fire_K2Node_InputActionEvent_3
     *         Flags  -> (BlueprintEvent)
     * Parameters:
     *         InputCore::FKey                                    Key                                                        (BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::InpActEvt_Fire_K2Node_InputActionEvent_3(const InputCore::FKey& Key)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.InpActEvt_Fire_K2Node_InputActionEvent_3"));
        
        ABuff_TekArmor_Gloves_C_InpActEvt_Fire_K2Node_InputActionEvent_3_Params params {};
        params.Key = Key;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.InpActEvt_Gamepad_LeftTrigger_K2Node_InputKeyEvent_4
     *         Flags  -> (BlueprintEvent)
     * Parameters:
     *         InputCore::FKey                                    Key                                                        (BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::InpActEvt_Gamepad_LeftTrigger_K2Node_InputKeyEvent_4(const InputCore::FKey& Key)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.InpActEvt_Gamepad_LeftTrigger_K2Node_InputKeyEvent_4"));
        
        ABuff_TekArmor_Gloves_C_InpActEvt_Gamepad_LeftTrigger_K2Node_InputKeyEvent_4_Params params {};
        params.Key = Key;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.InpActEvt_Gamepad_LeftTrigger_K2Node_InputKeyEvent_3
     *         Flags  -> (BlueprintEvent)
     * Parameters:
     *         InputCore::FKey                                    Key                                                        (BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::InpActEvt_Gamepad_LeftTrigger_K2Node_InputKeyEvent_3(const InputCore::FKey& Key)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.InpActEvt_Gamepad_LeftTrigger_K2Node_InputKeyEvent_3"));
        
        ABuff_TekArmor_Gloves_C_InpActEvt_Gamepad_LeftTrigger_K2Node_InputKeyEvent_3_Params params {};
        params.Key = Key;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.InpActEvt_Gamepad_RightTrigger_K2Node_InputKeyEvent_2
     *         Flags  -> (BlueprintEvent)
     * Parameters:
     *         InputCore::FKey                                    Key                                                        (BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::InpActEvt_Gamepad_RightTrigger_K2Node_InputKeyEvent_2(const InputCore::FKey& Key)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.InpActEvt_Gamepad_RightTrigger_K2Node_InputKeyEvent_2"));
        
        ABuff_TekArmor_Gloves_C_InpActEvt_Gamepad_RightTrigger_K2Node_InputKeyEvent_2_Params params {};
        params.Key = Key;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.InpActEvt_Gamepad_RightTrigger_K2Node_InputKeyEvent_1
     *         Flags  -> (BlueprintEvent)
     * Parameters:
     *         InputCore::FKey                                    Key                                                        (BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::InpActEvt_Gamepad_RightTrigger_K2Node_InputKeyEvent_1(const InputCore::FKey& Key)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.InpActEvt_Gamepad_RightTrigger_K2Node_InputKeyEvent_1"));
        
        ABuff_TekArmor_Gloves_C_InpActEvt_Gamepad_RightTrigger_K2Node_InputKeyEvent_1_Params params {};
        params.Key = Key;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.InpActEvt_Targeting_K2Node_InputActionEvent_2
     *         Flags  -> (BlueprintEvent)
     * Parameters:
     *         InputCore::FKey                                    Key                                                        (BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::InpActEvt_Targeting_K2Node_InputActionEvent_2(const InputCore::FKey& Key)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.InpActEvt_Targeting_K2Node_InputActionEvent_2"));
        
        ABuff_TekArmor_Gloves_C_InpActEvt_Targeting_K2Node_InputActionEvent_2_Params params {};
        params.Key = Key;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.InpActEvt_Targeting_K2Node_InputActionEvent_1
     *         Flags  -> (BlueprintEvent)
     * Parameters:
     *         InputCore::FKey                                    Key                                                        (BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::InpActEvt_Targeting_K2Node_InputActionEvent_1(const InputCore::FKey& Key)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.InpActEvt_Targeting_K2Node_InputActionEvent_1"));
        
        ABuff_TekArmor_Gloves_C_InpActEvt_Targeting_K2Node_InputActionEvent_1_Params params {};
        params.Key = Key;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.Server_SetPunchChargeState
     *         Flags  -> (Net, NetReliable, NetServer, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         E_TekGlovePunchState::E_TekGlovePunchState         newPunchState                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::Server_SetPunchChargeState(E_TekGlovePunchState::E_TekGlovePunchState newPunchState)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.Server_SetPunchChargeState"));
        
        ABuff_TekArmor_Gloves_C_Server_SetPunchChargeState_Params params {};
        params.newPunchState = newPunchState;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.ResetCanLandAfterDelay
     *         Flags  -> (BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::ResetCanLandAfterDelay()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.ResetCanLandAfterDelay"));
        
        ABuff_TekArmor_Gloves_C_ResetCanLandAfterDelay_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.UpdateClientHasValidRocketPunchTarget
     *         Flags  -> (Net, NetReliable, NetClient, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         bool                                               newHasTarget                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         ShooterGame::APrimalCharacter*                     newTargetPawn                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::UpdateClientHasValidRocketPunchTarget(bool newHasTarget, ShooterGame::APrimalCharacter* newTargetPawn)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.UpdateClientHasValidRocketPunchTarget"));
        
        ABuff_TekArmor_Gloves_C_UpdateClientHasValidRocketPunchTarget_Params params {};
        params.newHasTarget = newHasTarget;
        params.newTargetPawn = newTargetPawn;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.ResetPunchStateAfterDelay
     *         Flags  -> (BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::ResetPunchStateAfterDelay()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.ResetPunchStateAfterDelay"));
        
        ABuff_TekArmor_Gloves_C_ResetPunchStateAfterDelay_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.Gloves_AltFirePressed
     *         Flags  -> (BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::Gloves_AltFirePressed()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.Gloves_AltFirePressed"));
        
        ABuff_TekArmor_Gloves_C_Gloves_AltFirePressed_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.Gloves_AltFireReleased
     *         Flags  -> (BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::Gloves_AltFireReleased()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.Gloves_AltFireReleased"));
        
        ABuff_TekArmor_Gloves_C_Gloves_AltFireReleased_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.OnSuperPunch_Released
     *         Flags  -> (BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::OnSuperPunch_Released()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.OnSuperPunch_Released"));
        
        ABuff_TekArmor_Gloves_C_OnSuperPunch_Released_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.OnSuperPunch_Pressed
     *         Flags  -> (BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::OnSuperPunch_Pressed()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.OnSuperPunch_Pressed"));
        
        ABuff_TekArmor_Gloves_C_OnSuperPunch_Pressed_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.BP_OnOwnerTeleported
     *         Flags  -> (Event, Public, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::BP_OnOwnerTeleported()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.BP_OnOwnerTeleported"));
        
        ABuff_TekArmor_Gloves_C_BP_OnOwnerTeleported_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.ExecuteUbergraph_Buff_TekArmor_Gloves
     *         Flags  -> (Final, HasDefaults)
     * Parameters:
     *         int32_t                                            EntryPoint                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Gloves_C::ExecuteUbergraph_Buff_TekArmor_Gloves(int32_t EntryPoint)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Gloves.Buff_TekArmor_Gloves_C.ExecuteUbergraph_Buff_TekArmor_Gloves"));
        
        ABuff_TekArmor_Gloves_C_ExecuteUbergraph_Buff_TekArmor_Gloves_Params params {};
        params.EntryPoint = EntryPoint;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

}

#ifdef _MSC_VER
    #pragma pack(pop)
#endif
