/**
 * ----------------------------------------
 * |        Generated By CheatGear        |
 * ----------------------------------------
 * | Game:    ArkAscended                 |
 * | Version: 06.06.2025                  |
 * | Date:    06/06/2025                  |
 * ----------------------------------------
 */

#include "pch.h"
#include <cstdint>
#include <vector>
#include <string>
#include <locale>
#include <unordered_set>
#include "Headers/Global_DEFINES.h"
#include "Headers/BasicTypes.h"
#include "Headers/CoreUObject_UFunction.h"
#include "Headers/Buff_TekArmor_Helmet_PARAMS.h"
#include "Headers/Buff_TekArmor_Helmet_ABuff_TekArmor_Helmet_C.h"

#ifdef _MSC_VER
    #pragma pack(push, 0x01)
#endif

namespace CG::Buff_Tek<PERSON>rmor_Helmet
{
    // --------------------------------------------------
    // # Structs functions
    // --------------------------------------------------
    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.UpdateBuffs
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         int32_t                                            NewState                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::UpdateBuffs(int32_t NewState)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.UpdateBuffs"));
        
        ABuff_TekArmor_Helmet_C_UpdateBuffs_Params params {};
        params.NewState = NewState;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.Activate Buff
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         int32_t                                            Index                                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         bool                                               bActivate                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::ActivateBuff(int32_t Index, bool bActivate)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.Activate Buff"));
        
        ABuff_TekArmor_Helmet_C_ActivateBuff_Params params {};
        params.Index = Index;
        params.bActivate = bActivate;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.TryConsumeElement
     *         Flags  -> (Public, HasOutParms, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         bool                                               Successful                                                 (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::TryConsumeElement(bool* Successful)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.TryConsumeElement"));
        
        ABuff_TekArmor_Helmet_C_TryConsumeElement_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        if (Successful != nullptr)
            *Successful = params.Successful;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.BPClientDoMultiUse
     *         Flags  -> (Event, Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         Engine::APlayerController*                         ForPC                                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
     *         int32_t                                            ClientUseIndex                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::BPClientDoMultiUse(Engine::APlayerController* ForPC, int32_t ClientUseIndex)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.BPClientDoMultiUse"));
        
        ABuff_TekArmor_Helmet_C_BPClientDoMultiUse_Params params {};
        params.ForPC = ForPC;
        params.ClientUseIndex = ClientUseIndex;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.BPTryMultiUse
     *         Flags  -> (Event, Public, HasOutParms, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         Engine::APlayerController*                         ForPC                                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
     *         int32_t                                            UseIndex                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         bool                                               ReturnValue                                                (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     */
    bool ABuff_TekArmor_Helmet_C::BPTryMultiUse(Engine::APlayerController* ForPC, int32_t UseIndex)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.BPTryMultiUse"));
        
        ABuff_TekArmor_Helmet_C_BPTryMultiUse_Params params {};
        params.ForPC = ForPC;
        params.UseIndex = UseIndex;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        return params.ReturnValue;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.BPGetMultiUseEntries
     *         Flags  -> (Event, Public, HasOutParms, HasDefaults, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         Engine::APlayerController*                         ForPC                                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
     *         BasicTypes::TArray<Engine::FMultiUseEntry>         MultiUseEntries                                            (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm, ContainsInstancedReference)
     *         BasicTypes::TArray<Engine::FMultiUseEntry>         ReturnValue                                                (Parm, OutParm, ReturnParm, ContainsInstancedReference)
     */
    BasicTypes::TArray<Engine::FMultiUseEntry> ABuff_TekArmor_Helmet_C::BPGetMultiUseEntries(Engine::APlayerController* ForPC, BasicTypes::TArray<Engine::FMultiUseEntry> MultiUseEntries)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.BPGetMultiUseEntries"));
        
        ABuff_TekArmor_Helmet_C_BPGetMultiUseEntries_Params params {};
        params.ForPC = ForPC;
        params.MultiUseEntries = MultiUseEntries;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        return params.ReturnValue;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.ExternalVisorToggle
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::ExternalVisorToggle()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.ExternalVisorToggle"));
        
        ABuff_TekArmor_Helmet_C_ExternalVisorToggle_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.BPNotifyOtherBuffActivated
     *         Flags  -> (Event, Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         ShooterGame::APrimalBuff*                          OtherBuff                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::BPNotifyOtherBuffActivated(ShooterGame::APrimalBuff* OtherBuff)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.BPNotifyOtherBuffActivated"));
        
        ABuff_TekArmor_Helmet_C_BPNotifyOtherBuffActivated_Params params {};
        params.OtherBuff = OtherBuff;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.BPNotifyOtherBuffDeactivated
     *         Flags  -> (Event, Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         ShooterGame::APrimalBuff*                          OtherBuff                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::BPNotifyOtherBuffDeactivated(ShooterGame::APrimalBuff* OtherBuff)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.BPNotifyOtherBuffDeactivated"));
        
        ABuff_TekArmor_Helmet_C_BPNotifyOtherBuffDeactivated_Params params {};
        params.OtherBuff = OtherBuff;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.RefreshFullTekSuitBuff
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::RefreshFullTekSuitBuff()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.RefreshFullTekSuitBuff"));
        
        ABuff_TekArmor_Helmet_C_RefreshFullTekSuitBuff_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.BuffTickServer
     *         Flags  -> (Event, Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         float                                              DeltaTime                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::BuffTickServer(float DeltaTime)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.BuffTickServer"));
        
        ABuff_TekArmor_Helmet_C_BuffTickServer_Params params {};
        params.DeltaTime = DeltaTime;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.DeactivateVisor
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::DeactivateVisor()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.DeactivateVisor"));
        
        ABuff_TekArmor_Helmet_C_DeactivateVisor_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.UpdateTintColor
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         int32_t                                            Index                                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::UpdateTintColor(int32_t Index)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.UpdateTintColor"));
        
        ABuff_TekArmor_Helmet_C_UpdateTintColor_Params params {};
        params.Index = Index;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.SetCastedArmorPieceRef
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::SetCastedArmorPieceRef()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.SetCastedArmorPieceRef"));
        
        ABuff_TekArmor_Helmet_C_SetCastedArmorPieceRef_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.RemoveToggleBuffs
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::RemoveToggleBuffs()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.RemoveToggleBuffs"));
        
        ABuff_TekArmor_Helmet_C_RemoveToggleBuffs_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.BuffTickClient
     *         Flags  -> (Event, Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         float                                              DeltaTime                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::BuffTickClient(float DeltaTime)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.BuffTickClient"));
        
        ABuff_TekArmor_Helmet_C_BuffTickClient_Params params {};
        params.DeltaTime = DeltaTime;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.BPDeactivated
     *         Flags  -> (Event, Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         Engine::AActor*                                    ForInstigator                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::BPDeactivated(Engine::AActor* ForInstigator)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.BPDeactivated"));
        
        ABuff_TekArmor_Helmet_C_BPDeactivated_Params params {};
        params.ForInstigator = ForInstigator;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.BPActivated_1
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::BPActivated_1()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.BPActivated_1"));
        
        ABuff_TekArmor_Helmet_C_BPActivated_1_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.FadeIn__FinishedFunc
     *         Flags  -> (BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::FadeIn__FinishedFunc()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.FadeIn__FinishedFunc"));
        
        ABuff_TekArmor_Helmet_C_FadeIn__FinishedFunc_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.FadeIn__UpdateFunc
     *         Flags  -> (BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::FadeIn__UpdateFunc()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.FadeIn__UpdateFunc"));
        
        ABuff_TekArmor_Helmet_C_FadeIn__UpdateFunc_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.FadeOut__FinishedFunc
     *         Flags  -> (BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::FadeOut__FinishedFunc()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.FadeOut__FinishedFunc"));
        
        ABuff_TekArmor_Helmet_C_FadeOut__FinishedFunc_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.FadeOut__UpdateFunc
     *         Flags  -> (BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::FadeOut__UpdateFunc()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.FadeOut__UpdateFunc"));
        
        ABuff_TekArmor_Helmet_C_FadeOut__UpdateFunc_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.HelmetFadeIn
     *         Flags  -> (BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::HelmetFadeIn()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.HelmetFadeIn"));
        
        ABuff_TekArmor_Helmet_C_HelmetFadeIn_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.HelmetFadeOut
     *         Flags  -> (BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::HelmetFadeOut()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.HelmetFadeOut"));
        
        ABuff_TekArmor_Helmet_C_HelmetFadeOut_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.FadeInAndOut
     *         Flags  -> (BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::FadeInAndOut()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.FadeInAndOut"));
        
        ABuff_TekArmor_Helmet_C_FadeInAndOut_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.BackToInitialPress
     *         Flags  -> (BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::BackToInitialPress()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.BackToInitialPress"));
        
        ABuff_TekArmor_Helmet_C_BackToInitialPress_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.PlayAnim_Server
     *         Flags  -> (Net, NetReliable, NetServer, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         Engine::UAnimMontage*                              Montage                                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::PlayAnim_Server(Engine::UAnimMontage* Montage)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.PlayAnim_Server"));
        
        ABuff_TekArmor_Helmet_C_PlayAnim_Server_Params params {};
        params.Montage = Montage;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.PlayAnim_Multicast
     *         Flags  -> (Net, NetReliable, NetMulticast, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         Engine::UAnimMontage*                              Montage                                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::PlayAnim_Multicast(Engine::UAnimMontage* Montage)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.PlayAnim_Multicast"));
        
        ABuff_TekArmor_Helmet_C_PlayAnim_Multicast_Params params {};
        params.Montage = Montage;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.Server_AddPostProcessBuffToPlayer
     *         Flags  -> (Net, NetReliable, NetServer, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         int32_t                                            BuffIndex                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::Server_AddPostProcessBuffToPlayer(int32_t BuffIndex)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.Server_AddPostProcessBuffToPlayer"));
        
        ABuff_TekArmor_Helmet_C_Server_AddPostProcessBuffToPlayer_Params params {};
        params.BuffIndex = BuffIndex;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.Server_RemovePostProcessBuffFromPlayer
     *         Flags  -> (Net, NetReliable, NetServer, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         int32_t                                            BuffIndex                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::Server_RemovePostProcessBuffFromPlayer(int32_t BuffIndex)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.Server_RemovePostProcessBuffFromPlayer"));
        
        ABuff_TekArmor_Helmet_C_Server_RemovePostProcessBuffFromPlayer_Params params {};
        params.BuffIndex = BuffIndex;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.Server_RemoveAllPostProcessBuffs
     *         Flags  -> (Net, NetReliable, NetServer, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::Server_RemoveAllPostProcessBuffs()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.Server_RemoveAllPostProcessBuffs"));
        
        ABuff_TekArmor_Helmet_C_Server_RemoveAllPostProcessBuffs_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.RemovePostProcessBuff
     *         Flags  -> (BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         int32_t                                            Index                                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::RemovePostProcessBuff(int32_t Index)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.RemovePostProcessBuff"));
        
        ABuff_TekArmor_Helmet_C_RemovePostProcessBuff_Params params {};
        params.Index = Index;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.ExternalVisorOnOffToggle
     *         Flags  -> (BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::ExternalVisorOnOffToggle()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.ExternalVisorOnOffToggle"));
        
        ABuff_TekArmor_Helmet_C_ExternalVisorOnOffToggle_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.Server_MoveToNextState
     *         Flags  -> (Net, NetReliable, NetServer, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::Server_MoveToNextState()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.Server_MoveToNextState"));
        
        ABuff_TekArmor_Helmet_C_Server_MoveToNextState_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.Client_SetToggleState
     *         Flags  -> (Net, NetReliable, NetClient, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         int32_t                                            NewState                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::Client_SetToggleState(int32_t NewState)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.Client_SetToggleState"));
        
        ABuff_TekArmor_Helmet_C_Client_SetToggleState_Params params {};
        params.NewState = NewState;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.AddPostProcessBuffToPlayer
     *         Flags  -> (BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         int32_t                                            BuffIndex                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::AddPostProcessBuffToPlayer(int32_t BuffIndex)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.AddPostProcessBuffToPlayer"));
        
        ABuff_TekArmor_Helmet_C_AddPostProcessBuffToPlayer_Params params {};
        params.BuffIndex = BuffIndex;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.PlayTransitionvisuals
     *         Flags  -> (BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::PlayTransitionvisuals()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.PlayTransitionvisuals"));
        
        ABuff_TekArmor_Helmet_C_PlayTransitionvisuals_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.PlayAnims
     *         Flags  -> (BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::PlayAnims()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.PlayAnims"));
        
        ABuff_TekArmor_Helmet_C_PlayAnims_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.SetToggleState
     *         Flags  -> (BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         int32_t                                            NewState                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::SetToggleState(int32_t NewState)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.SetToggleState"));
        
        ABuff_TekArmor_Helmet_C_SetToggleState_Params params {};
        params.NewState = NewState;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.OnTogglePressed
     *         Flags  -> (BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::OnTogglePressed()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.OnTogglePressed"));
        
        ABuff_TekArmor_Helmet_C_OnTogglePressed_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.RemoveAllPostProcessBuffs
     *         Flags  -> (BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::RemoveAllPostProcessBuffs()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.RemoveAllPostProcessBuffs"));
        
        ABuff_TekArmor_Helmet_C_RemoveAllPostProcessBuffs_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.Server_Toggle_On_Off
     *         Flags  -> (Net, NetReliable, NetServer, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         bool                                               ForceOff                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::Server_Toggle_On_Off(bool ForceOff)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.Server_Toggle_On_Off"));
        
        ABuff_TekArmor_Helmet_C_Server_Toggle_On_Off_Params params {};
        params.ForceOff = ForceOff;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.MoveToNextState
     *         Flags  -> (BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::MoveToNextState()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.MoveToNextState"));
        
        ABuff_TekArmor_Helmet_C_MoveToNextState_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.ExecuteUbergraph_Buff_TekArmor_Helmet
     *         Flags  -> (Final, HasDefaults)
     * Parameters:
     *         int32_t                                            EntryPoint                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Helmet_C::ExecuteUbergraph_Buff_TekArmor_Helmet(int32_t EntryPoint)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Helmet.Buff_TekArmor_Helmet_C.ExecuteUbergraph_Buff_TekArmor_Helmet"));
        
        ABuff_TekArmor_Helmet_C_ExecuteUbergraph_Buff_TekArmor_Helmet_Params params {};
        params.EntryPoint = EntryPoint;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

}

#ifdef _MSC_VER
    #pragma pack(pop)
#endif
