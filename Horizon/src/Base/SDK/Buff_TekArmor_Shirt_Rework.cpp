/**
 * ----------------------------------------
 * |        Generated By CheatGear        |
 * ----------------------------------------
 * | Game:    ArkAscended                 |
 * | Version: 06.06.2025                  |
 * | Date:    06/06/2025                  |
 * ----------------------------------------
 */

#include "pch.h"
#include <cstdint>
#include <vector>
#include <string>
#include <locale>
#include <unordered_set>
#include "Headers/Global_DEFINES.h"
#include "Headers/BasicTypes.h"
#include "Headers/CoreUObject_UFunction.h"
#include "Headers/Buff_TekArmor_Shirt_Rework_PARAMS.h"
#include "Headers/Buff_TekArmor_Shirt_Rework_ABuff_TekArmor_Shirt_Rework_C.h"

#ifdef _MSC_VER
    #pragma pack(push, 0x01)
#endif

namespace CG::Buff_TekArmor_Shirt_Rework
{
    // --------------------------------------------------
    // # Structs functions
    // --------------------------------------------------
    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.ValidateThrusterState
     *         Flags  -> (Public, HasOutParms, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         E_TekThrusterState::E_TekThrusterState             NewState                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         bool                                               ValidState                                                 (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::ValidateThrusterState(E_TekThrusterState::E_TekThrusterState NewState, bool* ValidState)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.ValidateThrusterState"));
        
        ABuff_TekArmor_Shirt_Rework_C_ValidateThrusterState_Params params {};
        params.NewState = NewState;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        if (ValidState != nullptr)
            *ValidState = params.ValidState;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.UpdateMaxSpeedForUnderwaterHovering
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::UpdateMaxSpeedForUnderwaterHovering()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.UpdateMaxSpeedForUnderwaterHovering"));
        
        ABuff_TekArmor_Shirt_Rework_C_UpdateMaxSpeedForUnderwaterHovering_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.SetUnderwaterHovering
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         bool                                               bNewUnderwaterHovering                                     (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::SetUnderwaterHovering(bool bNewUnderwaterHovering)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.SetUnderwaterHovering"));
        
        ABuff_TekArmor_Shirt_Rework_C_SetUnderwaterHovering_Params params {};
        params.bNewUnderwaterHovering = bNewUnderwaterHovering;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.RegulateScaleFX
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         Engine::UParticleSystemComponent*                  psc                                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::RegulateScaleFX(Engine::UParticleSystemComponent* psc)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.RegulateScaleFX"));
        
        ABuff_TekArmor_Shirt_Rework_C_RegulateScaleFX_Params params {};
        params.psc = psc;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.ShouldUseAutonomousCorrectionOffset
     *         Flags  -> (Event, Public, HasOutParms, BlueprintCallable, BlueprintEvent, Const)
     * Parameters:
     *         bool                                               ReturnValue                                                (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     */
    bool ABuff_TekArmor_Shirt_Rework_C::ShouldUseAutonomousCorrectionOffset()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.ShouldUseAutonomousCorrectionOffset"));
        
        ABuff_TekArmor_Shirt_Rework_C_ShouldUseAutonomousCorrectionOffset_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        return params.ReturnValue;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.HideBoostIcon
     *         Flags  -> (Public, HasOutParms, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         bool                                               ShouldSet                                                  (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         bool                                               WithValue                                                  (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::HideBoostIcon(bool* ShouldSet, bool* WithValue)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.HideBoostIcon"));
        
        ABuff_TekArmor_Shirt_Rework_C_HideBoostIcon_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        if (ShouldSet != nullptr)
            *ShouldSet = params.ShouldSet;
        if (WithValue != nullptr)
            *WithValue = params.WithValue;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.SetJetpackOffset
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         CoreUObject::FTransform                            AddTransform                                               (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::SetJetpackOffset(const CoreUObject::FTransform& AddTransform)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.SetJetpackOffset"));
        
        ABuff_TekArmor_Shirt_Rework_C_SetJetpackOffset_Params params {};
        params.AddTransform = AddTransform;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.OnRep_JetPackVFXOffset
     *         Flags  -> (HasDefaults, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::OnRep_JetPackVFXOffset()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.OnRep_JetPackVFXOffset"));
        
        ABuff_TekArmor_Shirt_Rework_C_OnRep_JetPackVFXOffset_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.OnInputsPrevented
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::OnInputsPrevented()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.OnInputsPrevented"));
        
        ABuff_TekArmor_Shirt_Rework_C_OnInputsPrevented_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.BPOverrideCharacterNewFallVelocity
     *         Flags  -> (Event, Public, HasOutParms, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         CoreUObject::FVector                               InitialVelocity                                            (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ZeroConstructor, ReferenceParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         CoreUObject::FVector                               Gravity                                                    (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ZeroConstructor, ReferenceParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         float                                              DeltaTime                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         CoreUObject::FVector                               ReturnValue                                                (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     */
    CoreUObject::FVector ABuff_TekArmor_Shirt_Rework_C::BPOverrideCharacterNewFallVelocity(const CoreUObject::FVector& InitialVelocity, const CoreUObject::FVector& Gravity, float DeltaTime)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.BPOverrideCharacterNewFallVelocity"));
        
        ABuff_TekArmor_Shirt_Rework_C_BPOverrideCharacterNewFallVelocity_Params params {};
        params.InitialVelocity = InitialVelocity;
        params.Gravity = Gravity;
        params.DeltaTime = DeltaTime;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        return params.ReturnValue;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.Clamp LocationZ
     *         Flags  -> (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::ClampLocationZ()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.Clamp LocationZ"));
        
        ABuff_TekArmor_Shirt_Rework_C_ClampLocationZ_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.IsPlayerInWater
     *         Flags  -> (Public, HasOutParms, BlueprintCallable, BlueprintEvent, BlueprintPure)
     * Parameters:
     *         bool                                               Result                                                     (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::IsPlayerInWater(bool* Result)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.IsPlayerInWater"));
        
        ABuff_TekArmor_Shirt_Rework_C_IsPlayerInWater_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        if (Result != nullptr)
            *Result = params.Result;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.IsTekChargeThrusting
     *         Flags  -> (Public, HasOutParms, BlueprintCallable, BlueprintEvent, BlueprintPure)
     * Parameters:
     *         bool                                               Result                                                     (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::IsTekChargeThrusting(bool* Result)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.IsTekChargeThrusting"));
        
        ABuff_TekArmor_Shirt_Rework_C_IsTekChargeThrusting_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        if (Result != nullptr)
            *Result = params.Result;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.AreChargeThrustingAnimsPlaying
     *         Flags  -> (Public, HasOutParms, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         bool                                               cleanUpAnims                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         bool                                               Result                                                     (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::AreChargeThrustingAnimsPlaying(bool cleanUpAnims, bool* Result)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.AreChargeThrustingAnimsPlaying"));
        
        ABuff_TekArmor_Shirt_Rework_C_AreChargeThrustingAnimsPlaying_Params params {};
        params.cleanUpAnims = cleanUpAnims;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        if (Result != nullptr)
            *Result = params.Result;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.IsPlayerInjured
     *         Flags  -> (Public, HasOutParms, BlueprintCallable, BlueprintEvent, BlueprintPure)
     * Parameters:
     *         bool                                               Result                                                     (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::IsPlayerInjured(bool* Result)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.IsPlayerInjured"));
        
        ABuff_TekArmor_Shirt_Rework_C_IsPlayerInjured_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        if (Result != nullptr)
            *Result = params.Result;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.IsPlayerWaterRunning
     *         Flags  -> (Public, HasOutParms, BlueprintCallable, BlueprintEvent, BlueprintPure)
     * Parameters:
     *         bool                                               Result                                                     (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::IsPlayerWaterRunning(bool* Result)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.IsPlayerWaterRunning"));
        
        ABuff_TekArmor_Shirt_Rework_C_IsPlayerWaterRunning_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        if (Result != nullptr)
            *Result = params.Result;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.BPSetupForInstigator
     *         Flags  -> (Event, Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         Engine::AActor*                                    ForInstigator                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::BPSetupForInstigator(Engine::AActor* ForInstigator)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.BPSetupForInstigator"));
        
        ABuff_TekArmor_Shirt_Rework_C_BPSetupForInstigator_Params params {};
        params.ForInstigator = ForInstigator;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.UpdateBoostIconVisibility
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         bool                                               setVisible                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::UpdateBoostIconVisibility(bool setVisible)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.UpdateBoostIconVisibility"));
        
        ABuff_TekArmor_Shirt_Rework_C_UpdateBoostIconVisibility_Params params {};
        params.setVisible = setVisible;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.SetIsChargeThrusting
     *         Flags  -> (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         bool                                               Newval                                                     (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::SetIsChargeThrusting(bool Newval)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.SetIsChargeThrusting"));
        
        ABuff_TekArmor_Shirt_Rework_C_SetIsChargeThrusting_Params params {};
        params.Newval = Newval;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.CanUseTekAbility
     *         Flags  -> (Public, HasOutParms, HasDefaults, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         bool                                               bNotifyIfOutOfElement                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         bool                                               bResult                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::CanUseTekAbility(bool bNotifyIfOutOfElement, bool* bResult)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.CanUseTekAbility"));
        
        ABuff_TekArmor_Shirt_Rework_C_CanUseTekAbility_Params params {};
        params.bNotifyIfOutOfElement = bNotifyIfOutOfElement;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        if (bResult != nullptr)
            *bResult = params.bResult;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.UpdateThrusterFX
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::UpdateThrusterFX()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.UpdateThrusterFX"));
        
        ABuff_TekArmor_Shirt_Rework_C_UpdateThrusterFX_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.BPDeactivated
     *         Flags  -> (Event, Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         Engine::AActor*                                    ForInstigator                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::BPDeactivated(Engine::AActor* ForInstigator)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.BPDeactivated"));
        
        ABuff_TekArmor_Shirt_Rework_C_BPDeactivated_Params params {};
        params.ForInstigator = ForInstigator;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.Reset Owning PlayerVariables
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::ResetOwningPlayerVariables()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.Reset Owning PlayerVariables"));
        
        ABuff_TekArmor_Shirt_Rework_C_ResetOwningPlayerVariables_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.UpdateIsSwimming
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::UpdateIsSwimming()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.UpdateIsSwimming"));
        
        ABuff_TekArmor_Shirt_Rework_C_UpdateIsSwimming_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.ClampPlayerVelocity
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         double                                             maxVelocity                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::ClampPlayerVelocity(double maxVelocity)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.ClampPlayerVelocity"));
        
        ABuff_TekArmor_Shirt_Rework_C_ClampPlayerVelocity_Params params {};
        params.maxVelocity = maxVelocity;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.UpdateTekShirtByState
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::UpdateTekShirtByState()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.UpdateTekShirtByState"));
        
        ABuff_TekArmor_Shirt_Rework_C_UpdateTekShirtByState_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.OnRep_bIsUnderwater
     *         Flags  -> (BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::OnRep_bIsUnderwater()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.OnRep_bIsUnderwater"));
        
        ABuff_TekArmor_Shirt_Rework_C_OnRep_bIsUnderwater_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.RestoreThrusterStateFromCurrentInputs
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::RestoreThrusterStateFromCurrentInputs()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.RestoreThrusterStateFromCurrentInputs"));
        
        ABuff_TekArmor_Shirt_Rework_C_RestoreThrusterStateFromCurrentInputs_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.ResetBoostWhenGrounded
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::ResetBoostWhenGrounded()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.ResetBoostWhenGrounded"));
        
        ABuff_TekArmor_Shirt_Rework_C_ResetBoostWhenGrounded_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.IsPlayerUnderWater
     *         Flags  -> (Public, HasOutParms, BlueprintCallable, BlueprintEvent, BlueprintPure)
     * Parameters:
     *         bool                                               Result                                                     (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::IsPlayerUnderWater(bool* Result)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.IsPlayerUnderWater"));
        
        ABuff_TekArmor_Shirt_Rework_C_IsPlayerUnderWater_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        if (Result != nullptr)
            *Result = params.Result;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.NetSetCurrentThrusterState
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         E_TekThrusterState::E_TekThrusterState             NewState                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::NetSetCurrentThrusterState(E_TekThrusterState::E_TekThrusterState NewState)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.NetSetCurrentThrusterState"));
        
        ABuff_TekArmor_Shirt_Rework_C_NetSetCurrentThrusterState_Params params {};
        params.NewState = NewState;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.BuffTickServer
     *         Flags  -> (Event, Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         float                                              DeltaTime                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::BuffTickServer(float DeltaTime)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.BuffTickServer"));
        
        ABuff_TekArmor_Shirt_Rework_C_BuffTickServer_Params params {};
        params.DeltaTime = DeltaTime;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.JetpackBoostClient
     *         Flags  -> (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::JetpackBoostClient()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.JetpackBoostClient"));
        
        ABuff_TekArmor_Shirt_Rework_C_JetpackBoostClient_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.JetpackBoost_Server
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::JetpackBoost_Server()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.JetpackBoost_Server"));
        
        ABuff_TekArmor_Shirt_Rework_C_JetpackBoost_Server_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.Update Thruster Impulse
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::UpdateThrusterImpulse()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.Update Thruster Impulse"));
        
        ABuff_TekArmor_Shirt_Rework_C_UpdateThrusterImpulse_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.IsThrusterActive
     *         Flags  -> (Public, HasOutParms, BlueprintCallable, BlueprintEvent, BlueprintPure, Const)
     * Parameters:
     *         bool                                               Result                                                     (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::IsThrusterActive(bool* Result)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.IsThrusterActive"));
        
        ABuff_TekArmor_Shirt_Rework_C_IsThrusterActive_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
        
        if (Result != nullptr)
            *Result = params.Result;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.OnRep_CurrentThrusterState
     *         Flags  -> (BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::OnRep_CurrentThrusterState()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.OnRep_CurrentThrusterState"));
        
        ABuff_TekArmor_Shirt_Rework_C_OnRep_CurrentThrusterState_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.SetCastedArmorPieceRef
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::SetCastedArmorPieceRef()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.SetCastedArmorPieceRef"));
        
        ABuff_TekArmor_Shirt_Rework_C_SetCastedArmorPieceRef_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.BuffTickClient
     *         Flags  -> (Event, Public, HasDefaults, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         float                                              DeltaTime                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::BuffTickClient(float DeltaTime)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.BuffTickClient"));
        
        ABuff_TekArmor_Shirt_Rework_C_BuffTickClient_Params params {};
        params.DeltaTime = DeltaTime;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.Client_SetThrusterState
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         E_TekThrusterState::E_TekThrusterState             NewState                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::Client_SetThrusterState(E_TekThrusterState::E_TekThrusterState NewState)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.Client_SetThrusterState"));
        
        ABuff_TekArmor_Shirt_Rework_C_Client_SetThrusterState_Params params {};
        params.NewState = NewState;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.JetpackBoost_Start
     *         Flags  -> (Public, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::JetpackBoost_Start()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.JetpackBoost_Start"));
        
        ABuff_TekArmor_Shirt_Rework_C_JetpackBoost_Start_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.InpActEvt_Jump_K2Node_InputActionEvent_5
     *         Flags  -> (BlueprintEvent)
     * Parameters:
     *         InputCore::FKey                                    Key                                                        (BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::InpActEvt_Jump_K2Node_InputActionEvent_5(const InputCore::FKey& Key)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.InpActEvt_Jump_K2Node_InputActionEvent_5"));
        
        ABuff_TekArmor_Shirt_Rework_C_InpActEvt_Jump_K2Node_InputActionEvent_5_Params params {};
        params.Key = Key;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.InpActEvt_Jump_K2Node_InputActionEvent_4
     *         Flags  -> (BlueprintEvent)
     * Parameters:
     *         InputCore::FKey                                    Key                                                        (BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::InpActEvt_Jump_K2Node_InputActionEvent_4(const InputCore::FKey& Key)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.InpActEvt_Jump_K2Node_InputActionEvent_4"));
        
        ABuff_TekArmor_Shirt_Rework_C_InpActEvt_Jump_K2Node_InputActionEvent_4_Params params {};
        params.Key = Key;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.InpActEvt_Run_K2Node_InputActionEvent_3
     *         Flags  -> (BlueprintEvent)
     * Parameters:
     *         InputCore::FKey                                    Key                                                        (BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::InpActEvt_Run_K2Node_InputActionEvent_3(const InputCore::FKey& Key)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.InpActEvt_Run_K2Node_InputActionEvent_3"));
        
        ABuff_TekArmor_Shirt_Rework_C_InpActEvt_Run_K2Node_InputActionEvent_3_Params params {};
        params.Key = Key;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.InpActEvt_Run_K2Node_InputActionEvent_2
     *         Flags  -> (BlueprintEvent)
     * Parameters:
     *         InputCore::FKey                                    Key                                                        (BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::InpActEvt_Run_K2Node_InputActionEvent_2(const InputCore::FKey& Key)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.InpActEvt_Run_K2Node_InputActionEvent_2"));
        
        ABuff_TekArmor_Shirt_Rework_C_InpActEvt_Run_K2Node_InputActionEvent_2_Params params {};
        params.Key = Key;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.InpActEvt_RunToggle_K2Node_InputActionEvent_1
     *         Flags  -> (BlueprintEvent)
     * Parameters:
     *         InputCore::FKey                                    Key                                                        (BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::InpActEvt_RunToggle_K2Node_InputActionEvent_1(const InputCore::FKey& Key)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.InpActEvt_RunToggle_K2Node_InputActionEvent_1"));
        
        ABuff_TekArmor_Shirt_Rework_C_InpActEvt_RunToggle_K2Node_InputActionEvent_1_Params params {};
        params.Key = Key;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.InpActEvt_Gamepad_FaceButton_Bottom_K2Node_InputKeyEvent_2
     *         Flags  -> (BlueprintEvent)
     * Parameters:
     *         InputCore::FKey                                    Key                                                        (BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::InpActEvt_Gamepad_FaceButton_Bottom_K2Node_InputKeyEvent_2(const InputCore::FKey& Key)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.InpActEvt_Gamepad_FaceButton_Bottom_K2Node_InputKeyEvent_2"));
        
        ABuff_TekArmor_Shirt_Rework_C_InpActEvt_Gamepad_FaceButton_Bottom_K2Node_InputKeyEvent_2_Params params {};
        params.Key = Key;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.InpActEvt_Gamepad_FaceButton_Bottom_K2Node_InputKeyEvent_1
     *         Flags  -> (BlueprintEvent)
     * Parameters:
     *         InputCore::FKey                                    Key                                                        (BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::InpActEvt_Gamepad_FaceButton_Bottom_K2Node_InputKeyEvent_1(const InputCore::FKey& Key)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.InpActEvt_Gamepad_FaceButton_Bottom_K2Node_InputKeyEvent_1"));
        
        ABuff_TekArmor_Shirt_Rework_C_InpActEvt_Gamepad_FaceButton_Bottom_K2Node_InputKeyEvent_1_Params params {};
        params.Key = Key;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.Server_SetThrusterState
     *         Flags  -> (Net, NetReliable, NetServer, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         E_TekThrusterState::E_TekThrusterState             NewState                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::Server_SetThrusterState(E_TekThrusterState::E_TekThrusterState NewState)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.Server_SetThrusterState"));
        
        ABuff_TekArmor_Shirt_Rework_C_Server_SetThrusterState_Params params {};
        params.NewState = NewState;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.UpdateCanBoostAfterCooldown
     *         Flags  -> (BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::UpdateCanBoostAfterCooldown()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.UpdateCanBoostAfterCooldown"));
        
        ABuff_TekArmor_Shirt_Rework_C_UpdateCanBoostAfterCooldown_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.PlayBoostCooldownSound
     *         Flags  -> (BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::PlayBoostCooldownSound()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.PlayBoostCooldownSound"));
        
        ABuff_TekArmor_Shirt_Rework_C_PlayBoostCooldownSound_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.FlashThrusterDuringBoost
     *         Flags  -> (BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::FlashThrusterDuringBoost()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.FlashThrusterDuringBoost"));
        
        ABuff_TekArmor_Shirt_Rework_C_FlashThrusterDuringBoost_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.BoostCleanupAfterDuration
     *         Flags  -> (BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::BoostCleanupAfterDuration()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.BoostCleanupAfterDuration"));
        
        ABuff_TekArmor_Shirt_Rework_C_BoostCleanupAfterDuration_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.OnPlayerJump_Released
     *         Flags  -> (BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::OnPlayerJump_Released()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.OnPlayerJump_Released"));
        
        ABuff_TekArmor_Shirt_Rework_C_OnPlayerJump_Released_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.OnPlayerJump_Pressed
     *         Flags  -> (BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::OnPlayerJump_Pressed()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.OnPlayerJump_Pressed"));
        
        ABuff_TekArmor_Shirt_Rework_C_OnPlayerJump_Pressed_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.Server_UpdateJetpackVFXOffset
     *         Flags  -> (Net, NetReliable, NetServer, BlueprintCallable, BlueprintEvent)
     * Parameters:
     *         CoreUObject::FTransform                            NewOffset                                                  (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::Server_UpdateJetpackVFXOffset(const CoreUObject::FTransform& NewOffset)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.Server_UpdateJetpackVFXOffset"));
        
        ABuff_TekArmor_Shirt_Rework_C_Server_UpdateJetpackVFXOffset_Params params {};
        params.NewOffset = NewOffset;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.ReceiveBeginPlay
     *         Flags  -> (Event, Protected, BlueprintEvent)
     * Parameters:
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::ReceiveBeginPlay()
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.ReceiveBeginPlay"));
        
        ABuff_TekArmor_Shirt_Rework_C_ReceiveBeginPlay_Params params {};
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

    /**
     * Function:
     *         RVA    -> 0x018C8E70
     *         Name   -> Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.ExecuteUbergraph_Buff_TekArmor_Shirt_Rework
     *         Flags  -> (Final, HasDefaults)
     * Parameters:
     *         int32_t                                            EntryPoint                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
     *         void                                               ReturnValue
     */
    void ABuff_TekArmor_Shirt_Rework_C::ExecuteUbergraph_Buff_TekArmor_Shirt_Rework(int32_t EntryPoint)
    {
        static CoreUObject::UFunction* fn = nullptr;
        if (!fn)
            fn = CoreUObject::UObject::FindObject<CoreUObject::UFunction>(xorstr("Function /Game/PrimalEarth/CoreBlueprints/Buffs/Buff_TekArmor_Shirt_Rework.Buff_TekArmor_Shirt_Rework_C.ExecuteUbergraph_Buff_TekArmor_Shirt_Rework"));
        
        ABuff_TekArmor_Shirt_Rework_C_ExecuteUbergraph_Buff_TekArmor_Shirt_Rework_Params params {};
        params.EntryPoint = EntryPoint;
        
        auto flags = fn->FunctionFlags;
        CoreUObject::UObject::ProcessEvent(fn, &params);
        fn->FunctionFlags = flags;
    }

}

#ifdef _MSC_VER
    #pragma pack(pop)
#endif
