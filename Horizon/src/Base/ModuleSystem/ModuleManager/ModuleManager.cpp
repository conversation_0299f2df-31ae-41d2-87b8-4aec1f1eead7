#include "pch.h"
#include "ModuleManager.h"

#include "Base/ModuleSystem/Category.h"
#include "ModuleSystem/Modules/Combat/Aimbot/ModuleAimbot.h"
#include "ModuleSystem/Modules/Combat/InfiniteGrenades/ModuleInfiniteGrenades.h"
#include "ModuleSystem/Modules/Combat/NoHeat/ModuleNoHeat.h"
#include "ModuleSystem/Modules/Exploit/InfiniteC4/ModuleInfiniteC4.h"
#include "ModuleSystem/Modules/Exploit/LevelExploit/ModuleLevelExploit.h"
#include "ModuleSystem/Modules/Exploit/OpenLastInventory/ModuleOpenLastInventory.h"
#include "ModuleSystem/Modules/Exploit/PlantKiller/ModulePlantKiller.h"
#include "ModuleSystem/Modules/Exploit/TransmitterBag/ModuleTransmitterBag.h"
#include "ModuleSystem/Modules/Misc/AutoClaim/ModuleAutoClaim.h"
#include "ModuleSystem/Modules/Misc/AutoDrop/ModuleAutoDrop.h"
#include "ModuleSystem/Modules/Misc/AutoImprint/ModuleAutoImprint.h"
#include "ModuleSystem/Modules/Misc/AutoPickUp/ModuleAutoPickUp.h"
#include "ModuleSystem/Modules/Misc/AutoPinCode/ModuleAutoPinCode.h"
#include "ModuleSystem/Modules/Misc/Binocular/ModuleBinocular.h"
#include "ModuleSystem/Modules/Misc/DropAllItems/ModuleDropAllItems.h"
#include "ModuleSystem/Modules/Misc/ForceDepo/ModuleForceDepo.h"
#include "ModuleSystem/Modules/Misc/HigherAccessRange/ModuleHigherAccessRange.h"
#include "ModuleSystem/Modules/Movement/AirStuck/ModuleAirStuck.h"
#include "ModuleSystem/Modules/Movement/AutoJump/ModuleAutoJump.h"
#include "ModuleSystem/Modules/Movement/AutoRide/ModuleAutoRide.h"
#include "ModuleSystem/Modules/Movement/AutoSwim/ModuleAutoSwim.h"
#include "ModuleSystem/Modules/Movement/AutoWalk/ModuleAutoWalk.h"
#include "ModuleSystem/Modules/Movement/BetterTekSuit/ModuleBetterTekSuit.h"
#include "ModuleSystem/Modules/Movement/FlyBackwards/ModuleFlyBackwards.h"
#include "ModuleSystem/Modules/Movement/Ghost/ModuleGhost.h"
#include "ModuleSystem/Modules/Movement/InfiniteStamina/ModuleInfiniteStamina.h"
#include "ModuleSystem/Modules/Movement/InstantTurn/ModuleInstantTurn.h"
#include "ModuleSystem/Modules/Movement/Speed/ModuleSpeed.h"
#include "ModuleSystem/Modules/Player/AutoArmor/ModuleAutoArmor.h"
#include "ModuleSystem/Modules/Player/AutoCollect/ModuleAutoCollect.h"
#include "ModuleSystem/Modules/Player/AutoDemolish/ModuleAutoDemolish.h"
#include "ModuleSystem/Modules/Player/AutoPlace/ModuleAutoPlace.h"
#include "ModuleSystem/Modules/Player/DediFiller/ModuleDediFiller.h"
#include "ModuleSystem/Modules/Player/InfiniteWeight/ModuleInfiniteWeight.h"
#include "ModuleSystem/Modules/Render/Chams/ModuleChams.h"
#include "ModuleSystem/Modules/Render/DamageNumbers/ModuleDamageNumbers.h"
#include "ModuleSystem/Modules/Render/ESP/ModuleESP.h"
#include "ModuleSystem/Modules/Render/HUD/ModuleHUD.h"

namespace Base
{
	ModuleManager Manager;

	void ModuleManager::Initialize()
	{
		AddModule(new ModuleHUD(XOR("HUD"), XOR("Show information about the server on the screen."), VK_F8, Category::RENDER, XOR("https://i.imgur.com/N7rKWqg.png"), true));
		AddModule(new ModuleInfiniteGrenades(XOR("Infinite Grenades"), XOR("Unlimited grenades."), 0x00, Category::COMBAT, XOR("https://ark.wiki.gg/images/thumb/f/fb/Grenade.png/228px-Grenade.png")));
		AddModule(new ModuleAutoJump(XOR("Auto Jump"), XOR("Automatically jumps."), 0x00, Category::MOVEMENT, XOR("https://i.imgur.com/RXcYSsq.png")));
		AddModule(new ModuleAutoWalk(XOR("Auto Walk"), XOR("Automatically walks."), 0x00, Category::MOVEMENT, XOR("https://i.imgur.com/2J3Pb0U.png")));
		AddModule(new ModuleAutoRide(XOR("Auto Ride"), XOR("Automatically rides close dinos."), 0x00, Category::MOVEMENT, XOR("https://i.imgur.com/0M85gju.png")));
		AddModule(new ModuleTransmitterBag(XOR("Transmitter Bag"), XOR("Access a transmitter for upload/download through inventories/item caches."), 0x00, Category::EXPLOIT, XOR("https://i.imgur.com/nP38LA6.png")));
		AddModule(new ModuleNoHeat(XOR("No Heat"), XOR("Your tek rifle will never overheat."), 0x00, Category::COMBAT, XOR("https://i.imgur.com/2bTCMWv.png")));
		AddModule(new ModuleAutoPickUp(XOR("Auto Pick Up"), XOR("Automatically picks up items, c4 or bear traps from the ground."), 0x00, Category::PLAYER, XOR("https://imgur.com/GSBPIEM.png")));
		AddModule(new ModuleESP(XOR("ESP"), XOR("See players/dinos/structures through walls."), 0x00, Category::RENDER, XOR("https://i.imgur.com/9YYj2AQ.png")));
		AddModule(new ModuleAutoDrop(XOR("Auto Drop"), XOR("Automatically drops items from player bodies and containers nearby."), 0x00, Category::PLAYER, XOR("https://i.imgur.com/Urer5Ac.png")));
		AddModule(new ModuleAutoPinCode(XOR("Auto PIN Code"), XOR("Automatically sets PIN codes for turrets and structures nearby."), 0x00, Category::WORLD, XOR("https://i.imgur.com/jllDn8k.png")));
		AddModule(new ModuleHigherAccessRange(XOR("Higher Access Range"), XOR("Allows you to access structures and entities from further away."), 0x00, Category::PLAYER, XOR("https://i.imgur.com/07VoF8t.png")));
		AddModule(new ModuleAutoSwim(XOR("Auto Swim"), XOR("Automatically moves up, if in water."), 0x00, Category::MOVEMENT, XOR("https://i.imgur.com/Rq3DhDW.png")));
		AddModule(new ModuleFlyBackwards(XOR("Flyer Tapejara Mode"), XOR("Allows flyers to act like a tapejara and fly sideways and backwards."), 0x00, Category::MOVEMENT, XOR("https://i.imgur.com/CYUqGvE.png")));
		AddModule(new ModuleGhost(XOR("Ghost"), XOR("Turns you into ghost mode."), 0x00, Category::MOVEMENT, XOR("https://i.imgur.com/l3SKod5.png")));
		AddModule(new ModuleInstantTurn(XOR("Instant Turn"), XOR("Turns your dino instantly."), 0x00, Category::MOVEMENT, XOR("https://i.imgur.com/36cPUan.png")));
		AddModule(new ModuleAutoArmor(XOR("Auto Armor"), XOR("Automatically equips armor for you by durability or armor."), 0x00, Category::PLAYER, XOR("https://i.imgur.com/U7zs58o.png")));
		AddModule(new ModuleChams(XOR("Chams"), XOR("Show players and other entites through walls in different colors."), 0x00, Category::RENDER, XOR("https://i.imgur.com/ro1aNSH.png")));
		AddModule(new ModuleDamageNumbers(XOR("Damage Numbers"), XOR("Shows damage numbers."), 0x00, Category::RENDER, XOR("https://i.imgur.com/JZU6J5f.png")));
		AddModule(new ModuleBetterTekSuit(XOR("Better Tek Suit"), XOR("Use tek suit abilities faster and better."), 0x00, Category::MOVEMENT, XOR("https://i.imgur.com/epgYKzP.png")));
		AddModule(new ModuleLevelExploit(XOR("Level Exploit"), XOR("Collect the XP from all x2 explorer notes."), 0x00, Category::EXPLOIT, XOR("https://i.imgur.com/W6KbwNi.png")));
		AddModule(new ModuleInfiniteC4(XOR("Infinite C4"), XOR("Place infinite C4 on dinos without being limited to one C4."), 0x00, Category::EXPLOIT, XOR("https://i.imgur.com/hI7JPRh.png")));
		AddModule(new ModuleSpeed(XOR("Speed"), XOR("Speed."), 0x00, Category::MOVEMENT, XOR("https://ark.wiki.gg/images/thumb/f/fb/Grenade.png/228px-Grenade.png")));
		AddModule(new ModuleAirStuck(XOR("Air Stuck"), XOR("Air Stuck."), 0x00, Category::MOVEMENT, XOR("https://ark.wiki.gg/images/thumb/f/fb/Grenade.png/228px-Grenade.png")));
		AddModule(new ModuleAimbot(XOR("Aimbot"), XOR("Always hit your target with fabricated sniper etc."), 0x00, Category::COMBAT, XOR("https://i.imgur.com/u5eHs1n.png")));
		AddModule(new ModuleAutoDemolish(XOR("Auto Demolish"), XOR("Auto Demolish."), 0x00, Category::MISC, XOR("https://i.imgur.com/S7FM9ZJ.png")));
		AddModule(new ModuleDediFiller(XOR("Dedi Filler"), XOR("Fills dedis automatically."), 0x00, Category::MISC, XOR("https://i.imgur.com/S7FM9ZJ.png")));
		AddModule(new ModuleInfiniteWeight(XOR("Infinite Weight"), XOR("Allows you to move while being overweight."), 0x00, Category::MISC, XOR("https://i.imgur.com/S7FM9ZJ.png")));

		AddModule(new ModuleForceDepo(XOR("Force Depo"), XOR("Force Depo."), 0x00, Category::UNOFFICIAL, XOR("https://i.imgur.com/S7FM9ZJ.png")));
		AddModule(new ModuleOpenLastInventory(XOR("Open Last Inventory"), XOR("Open Last Inventory."), 0x00, Category::UNOFFICIAL, XOR("https://i.imgur.com/S7FM9ZJ.png")));
		AddModule(new ModulePlantKiller(XOR("Plant Killer"), XOR("Plant Killer."), 0x00, Category::UNOFFICIAL, XOR("https://i.imgur.com/S7FM9ZJ.png")));
		AddModule(new ModuleAutoCollect(XOR("Auto Collect"), XOR("Auto Collect."), 0x00, Category::UNOFFICIAL, XOR("https://i.imgur.com/S7FM9ZJ.png")));
		AddModule(new ModuleAutoPlace(XOR("Auto Place"), XOR("Auto Place."), 0x00, Category::UNOFFICIAL, XOR("https://i.imgur.com/S7FM9ZJ.png")));

		AddModule(new ModuleInfiniteStamina(XOR("Infinite Stamina"), XOR("Infinite Stamina."), 0x00, Category::UNOFFICIAL, XOR("https://i.imgur.com/S7FM9ZJ.png")));
		AddModule(new ModuleAutoImprint(XOR("Auto Imprint"), XOR("Auto Imprint."), 0x00, Category::MISC, XOR("https://i.imgur.com/S7FM9ZJ.png")));
		AddModule(new ModuleAutoClaim(XOR("Auto Claim"), XOR("Auto Claim."), 0x00, Category::MISC, XOR("https://i.imgur.com/S7FM9ZJ.png")));
		AddModule(new ModuleDropAllItems(XOR("Drop All Items"), XOR("Drop All Items."), 0x00, Category::MISC, XOR("https://i.imgur.com/S7FM9ZJ.png")));
		AddModule(new ModuleBinocular(XOR("Binocular"), XOR("Binocular."), 0x00, Category::MISC, XOR("https://i.imgur.com/S7FM9ZJ.png")));
	} //

	std::list<Module*> ModuleManager::GetModules()
	{
		return this->m_modules;
	}

	void ModuleManager::AddModule(Module* module)
	{
		Manager.m_modules.push_back(module);
	}
}
