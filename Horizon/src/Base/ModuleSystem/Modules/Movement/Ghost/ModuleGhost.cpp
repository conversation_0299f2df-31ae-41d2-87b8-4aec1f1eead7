#include "pch.h"
#include "ModuleGhost.h"

namespace Base
{
	void ModuleGhost::SetupSettings()
	{
	}

	void ModuleGhost::OnCallServerMovePacked(CG::Engine::UCharacterMovementComponent* thiz, FSavedMove_Character* NewMove, FSavedMove_Character* PendingMove, FSavedMove_Character* OldMove, bool* skip)
	{
		*skip = true;
	}

	void ModuleGhost::OnPostRender(CG::ShooterGame::UShooterGameViewportClient* viewport, CG::Engine::UCanvas* canvas, CG::Engine::UWorld* world, CG::Engine::UFont* font)
	{
		auto playerController = Global::GetPlayerController(world);
		if (!playerController) return;

		auto player = playerController->GetPlayerCharacter();
		if (!player) return;

		player->ClientCheatFly();
		player->bActorEnableCollision = false;
		player->SetActorEnableCollision(false);
	}

	void ModuleGhost::OnDisable(CG::Engine::UWorld* world)
	{
		auto playerController = Global::GetPlayerController(world);
		if (!playerController) return;

		auto player = playerController->GetPlayerCharacter();
		if (!player) return;

		player->ClientCheatWalk();
		player->bActorEnableCollision = true;
		player->SetActorEnableCollision(true);
		playerController->ServerCheckClientPossession();
		playerController->ServerAcknowledgePossession(nullptr);
		playerController->ServerCheckClientPossession();
	}

	void ModuleGhost::LoadSettings(nlohmann::json config)
	{
		Module::LoadSettings(config);
	}
}
