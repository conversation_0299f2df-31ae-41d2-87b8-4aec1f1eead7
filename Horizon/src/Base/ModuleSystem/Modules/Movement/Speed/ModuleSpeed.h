#pragma once

#include "pch.h"

namespace Base
{
	class ModuleSpeed final : public Module
	{
	public:
		ModuleSpeed(const std::string& name, const std::string& description, const int key_code, const Category category, const std::string& icon) : Module(name, description, key_code, category, icon) {}

		void SetupSettings() override;

		void LoadSettings(nlohmann::json config) override;

		void OnEnable(CG::Engine::UWorld* world) override;

		void OnDisable(CG::Engine::UWorld* world) override;

		void OnPostRender(CG::ShooterGame::UShooterGameViewportClient* viewport, CG::Engine::UCanvas* canvas, CG::Engine::UWorld* world, CG::Engine::UFont* font) override;

		void OnCallServerMovePacked(CG::Engine::UCharacterMovementComponent* thiz, FSavedMove_Character* NewMove, FSavedMove_Character* PendingMove, FSavedMove_Character* OldMove, bool* skip) override;

		void OnReplicateMoveToServer(CG::Engine::UCharacterMovementComponent* thiz, float* DeltaTime, CG::CoreUObject::FVector** NewAcceleration) override;

		void OnProcessRemoteFunction(void* Driver, CG::Engine::AActor* Actor, CG::CoreUObject::UFunction* Function, void* Parameters, void* OutParms, void* Stack, CG::CoreUObject::UObject* SubObject, bool* escape, void** newParams) override;

	private:
		int m_speed = 5;
		int m_ticks = 20;
	};

}