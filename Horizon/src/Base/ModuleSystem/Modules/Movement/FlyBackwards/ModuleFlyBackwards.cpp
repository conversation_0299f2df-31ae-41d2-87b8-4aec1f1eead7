#include "pch.h"
#include "ModuleFlyBackwards.h"

namespace Base
{
	void ModuleFlyBackwards::SetupSettings()
	{
	}

	void ModuleFlyBackwards::OnPostRender(CG::ShooterGame::UShooterGameViewportClient* viewport, CG::Engine::UCanvas* canvas, CG::Engine::UWorld* world, CG::Engine::UFont* font)
	{
		auto playerController = Global::GetPlayerController(world);
		if (playerController == nullptr || playerController->AcknowledgedPawn == nullptr) return;

		if (playerController->AcknowledgedPawn->IsA(CG::ShooterGame::APrimalDinoCharacter::StaticClass()))
		{
			const auto dino = static_cast<CG::ShooterGame::APrimalDinoCharacter*>(playerController->AcknowledgedPawn);

			if (dino == nullptr) return;

			dino->bFlyerDinoAllowBackwardsFlight = true;
			dino->bFlyerDinoAllowStrafing = true;
		}
	}

	void ModuleFlyBackwards::OnDisable(CG::Engine::UWorld* world)
	{
		auto playerController = Global::GetPlayerController(world);
		if (playerController == nullptr || playerController->AcknowledgedPawn == nullptr) return;

		if (playerController->AcknowledgedPawn->IsA(CG::ShooterGame::APrimalDinoCharacter::StaticClass()))
		{
			const auto dino = static_cast<CG::ShooterGame::APrimalDinoCharacter*>(playerController->AcknowledgedPawn);

			if (dino == nullptr) return;

			dino->bFlyerDinoAllowBackwardsFlight = false;
			dino->bFlyerDinoAllowStrafing = false;
		}
	}

	void ModuleFlyBackwards::LoadSettings(nlohmann::json config)
	{
		Module::LoadSettings(config);
	}
}
