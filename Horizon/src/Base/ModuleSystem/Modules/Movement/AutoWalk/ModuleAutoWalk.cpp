#include "pch.h"
#include "ModuleAutoWalk.h"

namespace Base
{
	void ModuleAutoWalk::SetupSettings()
	{
	}

	void ModuleAutoWalk::OnPostRender(CG::ShooterGame::UShooterGameViewportClient* viewport, CG::Engine::UCanvas* canvas, CG::Engine::UWorld* world, CG::Engine::UFont* font)
	{
		CG::ShooterGame::AShooterPlayerController* playerController = Global::GetPlayerController(world);
		if (playerController == nullptr || playerController->AcknowledgedPawn == nullptr) return;

		auto pawn = playerController->AcknowledgedPawn;

		if (pawn != nullptr && pawn->IsA(CG::ShooterGame::APrimalDinoCharacter::StaticClass()))
		{
			auto dino = static_cast<CG::ShooterGame::APrimalDinoCharacter*>(pawn);

			dino->MoveForward(1);
		}
		else if (pawn != nullptr && pawn->IsA(CG::ShooterGame::AShooterCharacter::StaticClass()))
		{
			auto player = static_cast<CG::ShooterGame::AShooterCharacter*>(pawn);

			player->MoveForward(1);
		}
	}

	void ModuleAutoWalk::LoadSettings(nlohmann::json config)
	{
		Module::LoadSettings(config);
	}
}