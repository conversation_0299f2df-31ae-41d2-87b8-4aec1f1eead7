#include "pch.h"
#include "ModuleAutoPickUp.h"

namespace Base
{
	void ModuleAutoPickUp::SetupSettings()
	{
		AddSetting(new CheckboxSetting(XOR("Pick Up C4"), m_pickC4, 1));
		AddSetting(new CheckboxSetting(XOR("Pick Up Bear Traps"), m_pickBearTraps, 2));
		AddSetting(new CheckboxSetting(XOR("Pick Up Items"), m_pickItems, 3));
		AddSetting(new CheckboxSetting(XOR("Pick Up Friendly Structures"), m_pickUpFriendly, 4));
	}

	void ModuleAutoPickUp::OnPostRender(CG::ShooterGame::UShooterGameViewportClient* viewport, CG::Engine::UCanvas* canvas, CG::Engine::UWorld* world, CG::Engine::UFont* font)
	{
		static std::chrono::steady_clock::time_point lastExecutionTime = std::chrono::steady_clock::now();
		auto currentTime = std::chrono::steady_clock::now();
		auto elapsedTime = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - lastExecutionTime).count();

		if (elapsedTime < 20) return;
		lastExecutionTime = currentTime;

		CG::ShooterGame::AShooterPlayerController* playerController = Global::GetPlayerController(world);
		if (playerController == nullptr || playerController->AcknowledgedPawn == nullptr) return;
		CG::ShooterGame::AShooterCharacter* selfPlayer = playerController->GetPlayerCharacter();
		if (selfPlayer == nullptr) return;
		CG::Engine::ULevel* persistentLevel = world->PersistentLevel;
		if (persistentLevel == nullptr || !persistentLevel->ActorsList.Count()) return;
		CG::BasicTypes::TArray<CG::Engine::AActor*> actorArray = persistentLevel->ActorsList;

		if (!selfPlayer->bIsAtMaxInventoryItems)
		{
			for (int i = 0; i < actorArray.Count(); i++)
			{
				auto currentActor = actorArray[i];
				{
					if (currentActor == nullptr) continue;
					if (currentActor->RootComponent == nullptr) continue;
				}

				if (currentActor->IsA(CG::DroppedItemGeneric::ADroppedItemGeneric_C::StaticClass()) && currentActor->GetDistanceTo(selfPlayer) < 2000 && m_pickItems)
				{
					playerController->ServerMultiUse(currentActor, 101);
					return;
				}

				/*if (currentActor->IsA(AC4Charge_C::StaticClass()) && currentActor->GetDistanceTo(selfPlayer) < 2000 && m_pickC4 && (m_pickUpFriendly || currentActor->TargetingTeam != selfPlayer->TargetingTeam))
				{
					playerController->ServerMultiUse(currentActor, 201);
					return;
				}

				if (currentActor->IsA(ABaseBearTrap_C::StaticClass()) && currentActor->GetDistanceTo(selfPlayer) < 2000 && m_pickBearTraps && (m_pickUpFriendly || currentActor->TargetingTeam != selfPlayer->TargetingTeam))
				{
					playerController->ServerMultiUse(currentActor, 201);
					return;
				}*/
			}
		}
	}

	void ModuleAutoPickUp::LoadSettings(nlohmann::json config)
	{
		Module::LoadSettings(config);

		m_pickC4 = GetCheckboxStateById(this->GetName(), 1, config);
		m_pickBearTraps = GetCheckboxStateById(this->GetName(), 2, config);
		m_pickItems = GetCheckboxStateById(this->GetName(), 3, config);
		m_pickUpFriendly = GetCheckboxStateById(this->GetName(), 4, config);
	}
}
