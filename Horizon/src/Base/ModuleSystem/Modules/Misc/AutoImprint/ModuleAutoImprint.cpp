#include "pch.h"
#include "ModuleAutoImprint.h"

void Base::ModuleAutoImprint::SetupSettings()
{
    Module::SetupSettings();
}

void Base::ModuleAutoImprint::LoadSettings(nlohmann::json config)
{
    Module::LoadSettings(config);
}

void Base::ModuleAutoImprint::OnPostRender(CG::ShooterGame::UShooterGameViewportClient* viewport, CG::Engine::UCanvas* canvas,
                                           CG::Engine::UWorld* world, CG::Engine::UFont* font)
{
	CG::ShooterGame::AShooterPlayerController* playerController = Global::GetPlayerController(world);
    if (playerController == nullptr || playerController->AcknowledgedPawn == nullptr) return;
	CG::ShooterGame::AShooterCharacter* selfPlayer = playerController->GetPlayerCharacter();
    if (selfPlayer == nullptr) return;
	CG::Engine::ULevel* persistentLevel = world->PersistentLevel;
    if (persistentLevel == nullptr || !persistentLevel->ActorsList.Count()) return;
	CG::BasicTypes::TArray<CG::Engine::AActor*> actorArray = persistentLevel->ActorsList;
    if (world->GameState == nullptr) return;
	CG::ShooterGame::AShooterGameState* state = (CG::ShooterGame::AShooterGameState*) world->GameState;

    for (int i = 0; i < actorArray.Count(); i++)
    {
        auto currentActor = actorArray[i];
        {
            if (currentActor == nullptr) continue;
            if (currentActor->RootComponent == nullptr) continue;
        }

        if(currentActor->IsA(CG::ShooterGame::APrimalDinoCharacter::StaticClass()))
        {
	        CG::ShooterGame::APrimalDinoCharacter* dino = (CG::ShooterGame::APrimalDinoCharacter*) currentActor;
            
            if(dino->BabyAge < 1)
            {
                if(dino->GetDistanceTo(selfPlayer) > 1000) continue;
                float time = (dino->BabyNextCuddleTime - state->NetworkTime);
                if (time <= 0 && time > -220000000)
                {
                    if (dino->BabyCuddleType == CG::ShooterGame::EBabyCuddleType::PET)
                    {
                        playerController->ServerMultiUse(dino, 139);
                    } else if (dino->GetBabyCuddleFood() && dino->BabyCuddleType != CG::ShooterGame::EBabyCuddleType::WALK)
                    {
                        const auto food = dino->GetBabyCuddleFood();
                        for (int z = 0; z < selfPlayer->MyInventoryComponent->InventoryItems.Count(); z++)
                        {
                            auto Item = selfPlayer->MyInventoryComponent->InventoryItems[z];

                            if (!Item)
                                continue;

                            if (Item->bIsEngram)
                                continue;

                            if (Item->Class == food->Class || (Item->GetItemShortName().ToString().find(XOR("Extraordinary")) != std::string::npos && food->GetFullName().find(XOR("Kibble")) != std::string::npos))
                            {
                                playerController->GetPlayerCharacter()->MyInventoryComponent->ServerAddItemToSlot(Item->ItemID, 9, true);
                                playerController->ServerMultiUse(dino, 139);
                                
                                break;
                            }
                        }
                    }
                }
            }
        }
    }
}
