#include "pch.h"
#include "ModuleDropAllItems.h"

void Base::ModuleDropAllItems::SetupSettings()
{
    Module::SetupSettings();
}

void Base::ModuleDropAllItems::LoadSettings(nlohmann::json config)
{
    Module::LoadSettings(config);
}

void Base::ModuleDropAllItems::OnPostRender(CG::ShooterGame::UShooterGameViewportClient* viewport, CG::Engine::UCanvas* canvas,
                                            CG::Engine::UWorld* world, CG::Engine::UFont* font)
{
	CG::ShooterGame::AShooterPlayerController* playerController = Global::GetPlayerController(world);
    if (playerController == nullptr || playerController->AcknowledgedPawn == nullptr) return;
    CG::ShooterGame::AShooterCharacter* selfPlayer = playerController->GetPlayerCharacter();
    if (selfPlayer == nullptr) return;

    if(inventory)
    {
	    CG::CoreUObject::UClass* clazz = nullptr;
        for(int i = 0; i < inventory->InventoryItems.Count(); i++)
        {
            auto item = inventory->InventoryItems[i];
            if(!item) continue;
            if(item->bIsEngram) continue;

            if(item->ItemID.ItemID1 == netInfo.ItemID1 || item->ItemID.ItemID2 == netInfo.ItemID2)
            {
                clazz = item->Class;
                break;
            }
        }
        if(clazz)
        {
            for(int i = 0; i < inventory->InventoryItems.Count(); i++)
            {
                auto item = inventory->InventoryItems[i];
                if(!item) continue;
                if(item->bIsEngram) continue;

                if(item->IsA(clazz))
                {
                    playerController->ServerDropFromRemoteInventory(inventory, item->ItemID);
                }
            }
        }
        inventory = nullptr;
    }
}

void Base::ModuleDropAllItems::OnProcessRemoteFunction(void* Driver, CG::Engine::AActor* Actor, CG::CoreUObject::UFunction* Function,
    void* Parameters, void* OutParms, void* Stack, CG::CoreUObject::UObject* SubObject, bool* escape, void** newParams)
{
    if (Function->GetFullName().find(XOR("ServerDropFromRemoteInventory")) != std::string::npos)
    {
	    CG::ShooterGame::AShooterPlayerController_ServerDropFromRemoteInventory_Params* params = (CG::ShooterGame::AShooterPlayerController_ServerDropFromRemoteInventory_Params*) Parameters;
        if(this->IsEnabled())
        {
            netInfo = params->ItemID;
            inventory = params->InventoryComp;
        }
    }
    Module::OnProcessRemoteFunction(Driver, Actor, Function, Parameters, OutParms, Stack, SubObject, escape, newParams);
}
