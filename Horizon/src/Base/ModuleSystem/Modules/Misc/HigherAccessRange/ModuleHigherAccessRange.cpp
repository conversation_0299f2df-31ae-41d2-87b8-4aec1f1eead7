#include "pch.h"
#include "ModuleHigherAccessRange.h"

namespace Base
{
	void ModuleHigherAccessRange::SetupSettings()
	{
	}

	void ModuleHigherAccessRange::OnPostRender(CG::ShooterGame::UShooterGameViewportClient* viewport, CG::Engine::UCanvas* canvas, CG::Engine::UWorld* world, CG::Engine::UFont* font)
	{
		static std::chrono::steady_clock::time_point last_execution_time = std::chrono::steady_clock::now();
		auto currentTime = std::chrono::steady_clock::now();
		auto elapsedTime = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - last_execution_time).count();

		if (elapsedTime < 1000) return;
		last_execution_time = currentTime;

		CG::ShooterGame::AShooterPlayerController* playerController = Global::GetPlayerController(world);
		if (playerController == nullptr || playerController->AcknowledgedPawn == nullptr) return;
		CG::ShooterGame::AShooterCharacter* selfPlayer = playerController->GetPlayerCharacter();
		if (selfPlayer == nullptr) return;
		CG::Engine::ULevel* persistentLevel = world->PersistentLevel;
		if (persistentLevel == nullptr || !persistentLevel->ActorsList.Count()) return;
		CG::BasicTypes::TArray<CG::Engine::AActor*> actorArray = persistentLevel->ActorsList;

		playerController->MaxUseDistance = 999999;

		for (int i = 0; i < actorArray.Count(); i++)
		{
			auto currentActor = actorArray[i];
			{
				if (currentActor == nullptr) continue;
			}

			if (currentActor->IsA(CG::ShooterGame::APrimalCharacter::StaticClass()))
			{
				auto actor = static_cast<CG::ShooterGame::APrimalCharacter*>(currentActor);

				actor->FullIKDistance = 999999;
				actor->MaxDragDistance = 999999;
			}
			if (currentActor->IsA(CG::ShooterGame::APrimalStructureItemContainer::StaticClass()))
			{
				auto actor = static_cast<CG::ShooterGame::APrimalStructureItemContainer*>(currentActor);

				actor->MaxActivationDistance = 999999;

				if (actor->MyInventoryComponent)
				{
					actor->MyInventoryComponent->MaxInventoryAccessDistance = 999999;
					actor->MyInventoryComponent->MaxRemoteInventoryViewingDistance = 999999;
				}
			}
			if (currentActor->IsA(CG::ShooterGame::APrimalDinoCharacter::StaticClass()))
			{
				auto actor = static_cast<CG::ShooterGame::APrimalDinoCharacter*>(currentActor);

				actor->AllowRidingMaxDistance = 100000000000;
				actor->bUsePlayerMountedCarryingDinoAnimation = false;
			}
		}
	}

	void ModuleHigherAccessRange::LoadSettings(nlohmann::json config)
	{
		Module::LoadSettings(config);
	}
}
