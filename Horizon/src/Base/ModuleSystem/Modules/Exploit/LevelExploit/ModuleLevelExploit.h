#pragma once

#include "pch.h"

namespace Base
{
	class ModuleLevelExploit final : public Module
	{
	public:
		ModuleLevelExploit(const std::string& name, const std::string& description, const int key_code, const Category category, const std::string& icon) : Module(name, description, key_code, category, icon) {}

		void SetupSettings() override;

		void LoadSettings(nlohmann::json config) override;

		void OnEnable(CG::Engine::UWorld* world) override;
	};

}
