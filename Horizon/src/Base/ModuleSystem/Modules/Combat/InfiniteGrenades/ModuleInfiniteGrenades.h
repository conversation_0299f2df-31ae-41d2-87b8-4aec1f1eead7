#pragma once

#include "pch.h"

namespace Base
{
	class ModuleInfiniteGrenades final : public Module
	{
	public:
		ModuleInfiniteGrenades(const std::string& name, const std::string& description, const int key_code, const Category category, const std::string& icon) : Module(name, description, key_code, category, icon) {}

		void SetupSettings() override;

		void LoadSettings(nlohmann::json config) override;

		void OnProcessRemoteFunction(void* Driver, CG::Engine::AActor* Actor, CG::CoreUObject::UFunction* Function, void* Parameters, void* OutParms, void* Stack, CG::CoreUObject::UObject* SubObject, bool* escape, void** newParams) override;

		void OnPostRender(CG::ShooterGame::UShooterGameViewportClient* viewport, CG::Engine::UCanvas* canvas, CG::Engine::UWorld* world, CG::Engine::UFont* font) override;
	private:
		bool m_autoThrow = false;
	};

}
