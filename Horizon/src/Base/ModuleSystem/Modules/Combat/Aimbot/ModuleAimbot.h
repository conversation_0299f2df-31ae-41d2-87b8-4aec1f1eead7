#pragma once

#include "pch.h"

namespace Base
{
	class ModuleAimbot final : public Module
	{
	public:
		struct HitTracer
		{
			CG::CoreUObject::FVector p1;
			CG::CoreUObject::FVector p2;
			unsigned long long timestamp;

			HitTracer(CG::CoreUObject::FVector start, CG::CoreUObject::FVector end, unsigned long long time) : p1(start), p2(end), timestamp(time) {}
		};

		ModuleAimbot(const std::string& name, const std::string& description, const int key_code, const Category category, const std::string& icon) : Module(name, description, key_code, category, icon) {}

		void SetupSettings() override;

		void LoadSettings(nlohmann::json config) override;

		bool CalcRicochet(CG::ShooterGame::AShooterCharacter* resultant_target_entity, CG::CoreUObject::FVector muzzle_location, CG::CoreUObject::FVector& bone_pos, CG::CoreUObject::FVector* shoot_dir, float* out_new_distance);

		PBYTE FindWeaponTrace();

		CG::CoreUObject::FVector PredictPing(CG::ShooterGame::AShooterCharacter* player, CG::CoreUObject::FVector boneLocation, bool fromAimbot);

		CG::CoreUObject::FRotator LookAt(CG::Engine::AActor* Player, CG::Engine::AActor* Target, CG::ShooterGame::AShooterPlayerController* PlayerController);
		CG::CoreUObject::FRotator VectorToRotator(CG::CoreUObject::FVector lookDirection);

		void OnGetAdjustedAim(CG::ShooterGame::AShooterWeapon* Weapon, CG::CoreUObject::FVector* Result, int64_t a3, bool a4) override;

		void OnPostRender(CG::ShooterGame::UShooterGameViewportClient* viewport, CG::Engine::UCanvas* canvas, CG::Engine::UWorld* world, CG::Engine::UFont* font) override;

		void OnProcessRemoteFunction(void* Driver, CG::Engine::AActor* Actor, CG::CoreUObject::UFunction* Function, void* Parameters, void* OutParms, void* Stack, CG::CoreUObject::UObject* SubObject, bool* escape, void** newParams) override;

		void OnGetPlayerViewPoint(CG::Engine::AController* thiz, CG::CoreUObject::FVector* out_Location, CG::CoreUObject::FRotator* out_Rotation, bool* skip) override;

		CG::ShooterGame::APrimalCharacter* GetTarget();
		void SetTarget(CG::ShooterGame::APrimalCharacter* target);

		int* GetBoneId();
		void SetBoneId(int boneId);

		float* GetPrediction();
		void SetPrediction(float prediction);

		bool* IsLockAimbot();
		void SetLockAimbot(bool state);

		int GetFov();

		int ConvertBoneId(int bone);

		CG::CoreUObject::FVector CalculatePredictedEnemyPosition(CG::ShooterGame::AShooterCharacter* selfPlayer);

		bool IsEqual(const std::vector<unsigned char>& a, const std::vector<unsigned char>& b);
		bool IsFriendly(int32_t targetingTeam);
		void AddToAllyList(int32_t targetingTeam);
		void RemoveFromAllyList(int32_t targetingTeam);
		void AddOrRemoveWithMiddleMouseButton(CG::ShooterGame::AShooterCharacter* ret);

	private:
		int m_sliderFov = 300;
		int m_sliderThickness = 1;
		int m_sliderTriggerbotFov = 25;
		int m_rapidFireSlider = 5;

		int m_aimbotPrediction = 12;

		CG::ShooterGame::APrimalCharacter* m_target = nullptr;
		int m_boneId = 2;
		float m_prediction = 0.005f;
		bool m_showFov = true;
		bool m_lockAimbot = false;
		bool m_astroAimbot = false;
		bool m_targetFriendly = false;
		bool m_allyList = true;
		bool m_shootLowDura = false;
		bool m_triggerbot = false;
		bool m_shieldBypass = true;
		int m_keyLockAimbot = VK_RBUTTON;
		int m_keySwitchBone = 0x00;
		std::vector<int32_t> m_allies;
		bool m_wasKeyDown = false;
		bool m_targetHoversails = true;
		bool m_tekBowAimbot = false;
		bool m_tekBowRapidFire = false;
		bool m_noDelay = false;
		bool m_rapidFire = false;
		bool m_lock = true;
		bool m_disableLockAimOptimizations = false;

		std::vector<HitTracer> tracers;
		int duration = 2000; // Default duration in milliseconds
		float lineThickness = 1.0f;
		float color[4] = { 1.0f, 0.0f, 0.0f, 1.0f }; // Default color (red)

		CG::CoreUObject::FLinearColor m_fovColor = { 1.f, 1.f, 1.f, 1.f };
		std::string m_bone = XOR("Head");
		std::string m_predictionMethod = XOR("Simple");

		CG::CoreUObject::FRotator lastKnownRot, deltaRot;
		int deltaTick = 0;
	};

}
