#pragma once

#include "pch.h"

namespace Base
{
	class ModuleAutoArmor final : public Module
	{
	public:
		ModuleAutoArmor(const std::string& name, const std::string& description, const int key_code, const Category category, const std::string& icon) : Module(name, description, key_code, category, icon) {}

		void SetupSettings() override;

		void LoadSettings(nlohmann::json config) override;

		void OnPostRender(CG::ShooterGame::UShooterGameViewportClient* viewport, CG::Engine::UCanvas* canvas, CG::Engine::UWorld* world, CG::Engine::UFont* font) override;

		void GetCurrentEquippedArmor(CG::Engine::UWorld* world);

		void GetArmor(CG::Engine::UWorld* world);
	private:
		std::string m_helmet = XOR("Tek");
		std::string m_chestpiece = XOR("Tek");
		std::string m_gauntlets = XOR("Tek");
		std::string m_leggings = XOR("Tek");
		std::string m_boots = XOR("Tek");

		bool m_ignoreFed = false;
	};

}
