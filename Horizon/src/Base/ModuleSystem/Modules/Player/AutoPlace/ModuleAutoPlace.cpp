#include "pch.h"
#include "ModuleAutoPlace.h"

#include "Renderer/Renderer.h"
#include "Renderer/Vector.h"

namespace Base
{
    void ModuleAutoPlace::SetupSettings()
    {
        AddSetting(new CheckboxSetting(XOR("Enabled"), m_enabled, 1));
        AddSetting(new SliderSetting(XOR("Demolish Distance"), m_demolishDistance, 0.0, 5000.0, 2));
        AddSetting(new CheckboxSetting(XOR("Demolish Only Selected Structure Type"), m_demolishOnlySelected, 3));
    }

    void ModuleAutoPlace::OnPostRender(CG::ShooterGame::UShooterGameViewportClient* viewport, CG::Engine::UCanvas* canvas, CG::Engine::UWorld* world, CG::Engine::UFont* font)
    {
        CG::ShooterGame::AShooterPlayerController* playerController = Global::GetPlayerController(world);
        if (playerController == nullptr || playerController->PlayerCameraManager == nullptr || !playerController->GetPlayerCharacter()) return;

        CG::ShooterGame::AShooterCharacter* selfPlayer = playerController->GetPlayerCharacter();
        if (selfPlayer == nullptr) return;

        CG::Engine::ULevel* persistentLevel = world->PersistentLevel;
        if (persistentLevel == nullptr || !persistentLevel->ActorsList.Count()) return;

        CG::BasicTypes::TArray<CG::Engine::AActor*> actorArray = persistentLevel->ActorsList;

        CG::Engine::AHUD* hud = playerController->GetShooterHud();
        if (!hud) return;

        hud->Canvas = canvas;

        // Static variables to track state
        static int currentIndex = 0;
        static CG::CoreUObject::UClass* currClass = nullptr;
        static int screenX = 0;
        static int screenY = 0;

        // Get viewport size once
        if (screenX == 0 && screenY == 0) {
            playerController->GetViewportSize(&screenX, &screenY);
        }

        // Display current selected structure
        std::wstring infoText;
        if (currClass) {
            infoText = XOR(L"[") + std::to_wstring(currentIndex) + XOR(L" / ") + Global::ConvertUtf8ToUtf162(currClass->GetName()) + XOR(L"] ");
        }
        else {
            infoText = XOR(L"[No Structure] ");
        }
        infoText += XOR(L"Press 'C' to reset Structure Index");

        // Draw current selection text
        float y = 175.0f;
        DrawTextY(hud, infoText.c_str(), screenX / 2.0f, y, 1, { 1.0f, 1.0f, 1.0f, 1.0f }, font, 1.0f, &y);

        // Check for reset key press (C)
        if (playerController->WasInputKeyJustPressed(CG::InputCore::FKey(CG::BasicTypes::FName("C"))))
        {
            currentIndex = 0;
            currClass = nullptr;
        }

        // Rate limit execution
        static std::chrono::steady_clock::time_point lastExecutionTime = std::chrono::steady_clock::now();
        auto currentTime = std::chrono::steady_clock::now();
        auto elapsedTime = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - lastExecutionTime).count();
        if (elapsedTime < 350) return;
        lastExecutionTime = currentTime;

        // Set up placement variables
        CG::CoreUObject::FVector placeLocation = { 0, 0, 0 };

        // If we have a selected structure class
        if (currClass)
        {
            for (int i = 0; i < actorArray.Count(); i++)
            {
                auto currentActor = actorArray[i];

                if (currentActor == nullptr) continue;
                if (currentActor->RootComponent == nullptr) continue;

                if (currentActor->IsA(CG::ShooterGame::APrimalStructure::StaticClass()))
                {
                    float distance = Vector3(
                        selfPlayer->K2_GetActorLocation().X,
                        selfPlayer->K2_GetActorLocation().Y,
                        selfPlayer->K2_GetActorLocation().Z
                    ).Distance(Vector3(
                        currentActor->K2_GetActorLocation().X,
                        currentActor->K2_GetActorLocation().Y,
                        currentActor->K2_GetActorLocation().Z
                    )) / 100.0f;

                    if (distance > 16.0f) continue;

                    // Check for dedicated storage class
                    if (currClass->GetName().find(XOR("Dedi")) != std::string::npos && currentActor->IsA(CG::BP_DedicatedStorage::ABP_DedicatedStorage_C::StaticClass()))
                    {
                        auto dedi = static_cast<CG::BP_DedicatedStorage::ABP_DedicatedStorage_C*>(currentActor);
                        if (dedi && dedi->SelectedResourceClass) continue;
                    }

                    // Check if class matches selected
                    if (currentActor->Class->GetName() == currClass->GetName() || currentActor->IsA(currClass))
                    {
                        placeLocation = currentActor->K2_GetActorLocation();
                        //playerController->ServerMultiUse(currentActor, 203);
                    }
                }
            }
        }

        // Update structure placer info
        if (auto placer = playerController->StructurePlacer)
        {
            if (placer->CurrentPlacingStructure && placer->CurrentPlacingStructureIndex > 0)
            {
                currentIndex = placer->CurrentPlacingStructureIndex;
                currClass = placer->CurrentPlacingStructure->Class;
            }
        }

        // If no placement location, trace to find one
        if (placeLocation.X == 0)
        {
            auto camLoc = playerController->PlayerCameraManager->GetCameraLocation();
            auto camRot = playerController->PlayerCameraManager->GetCameraRotation();

            CG::CoreUObject::FVector traceStart = camLoc;
            CG::CoreUObject::FVector forwardVector;
            forwardVector.X = cos(camRot.Pitch * (3.14159f / 180.0f)) * cos(camRot.Yaw * (3.14159f / 180.0f));
            forwardVector.Y = cos(camRot.Pitch * (3.14159f / 180.0f)) * sin(camRot.Yaw * (3.14159f / 180.0f));
            forwardVector.Z = sin(camRot.Pitch * (3.14159f / 180.0f));
            CG::CoreUObject::FVector traceEnd = traceStart + (forwardVector * 100000.0f);

            CG::Engine::FHitResult hitResult;
            auto victoryCore = Global::GetVictoryCore();
            victoryCore->STATIC_VTraceSingleBP(world, &hitResult, traceStart, traceEnd, CG::Engine::ECollisionChannel::ECC_GameTraceChannel1, 0, CG::BasicTypes::FName(), false, selfPlayer, 0);

            placeLocation = hitResult.ImpactPoint;
        }

        // Place structure if index is valid
        if (currentIndex > 0)
        {
            CG::Engine::FBPNetExecParams params = {};
            CG::ShooterGame::FPlacementData placementData = {};

            // Special handling for TekBridge
            if (currClass && currClass->GetName().find(XOR("TekBridge")) != std::string::npos)
            {
                params.FloatParam1 = placeLocation.X - 1000.0f;
                params.FloatParam2 = placeLocation.Y - 1000.0f;
                params.FloatParam3 = placeLocation.Z;
            }

            if (!m_enabled && currClass)
            {
                for (int i = 0; i < selfPlayer->MyInventoryComponent->InventoryItems.Count(); i++)
                {
                    CG::ShooterGame::UPrimalItem* item = selfPlayer->MyInventoryComponent->InventoryItems[i];

                    if (!item->bIsBlueprint && !item->bIsItemSkin && item->ItemQuantity == 0 && item->IsA(CG::PrimalItemStructureGeneric::UPrimalItemStructureGeneric_C::StaticClass()))
                    {
                        playerController->ServerRequestPlaceStructure(
                            currentIndex,
                            placeLocation,
                            { 0, 0, 0 },
                            { 0, 0, 0 },
                            item->ItemID,
                            placementData,
                            params,
                            false,
                            false,
                            0
                        );

                        break;
                    }
                }
            }
            else
            {
                playerController->ServerRequestPlaceStructure(
                    currentIndex,
                    placeLocation,
                    { 0, 0, 0 },
                    { 0, 0, 0 },
                    { 0, 0 },
                    placementData,
                    params,
                    false,
                    false,
                    0
                );
            }
        }

        // Handle auto demolish based on distance
        if (!m_demolishOnlySelected || currClass == nullptr)
        {
            for (int i = 0; i < actorArray.Count(); i++)
            {
                auto currentActor = actorArray[i];

                if (currentActor == nullptr) continue;
                if (currentActor->RootComponent == nullptr) continue;

                float distance = Vector3(
                    selfPlayer->K2_GetActorLocation().X,
                    selfPlayer->K2_GetActorLocation().Y,
                    selfPlayer->K2_GetActorLocation().Z
                ).Distance(Vector3(
                    currentActor->K2_GetActorLocation().X,
                    currentActor->K2_GetActorLocation().Y,
                    currentActor->K2_GetActorLocation().Z
                )) / 100.0f;

                if (distance < m_demolishDistance) continue;

                if (currentActor->IsA(CG::ShooterGame::APrimalStructure::StaticClass()))
                {
                    auto structure = static_cast<CG::ShooterGame::APrimalStructure*>(currentActor);
                    playerController->ServerMultiUse(structure, 201);
                }
            }
        }
    }

    // Helper method for drawing text (adapted from ESP module)
    void ModuleAutoPlace::DrawTextY(CG::Engine::AHUD* hud, CG::BasicTypes::FString text, float x, float y, uint8_t alignment, CG::CoreUObject::FLinearColor color, CG::Engine::UFont* font, float scale, float* yValue)
    {
        float width;
        float height;

        CG::CoreUObject::FVector2D screenPos = { x, y };

        if (Renderer::IsInScreen(screenPos))
        {
            hud->GetTextSize(text, &width, &height, font, scale);

            if (text.IsValid() && text.ToString().length() != 0)
            {
                DrawText_(hud, text, x, y, alignment, color, font, scale);
                *yValue += height / 2 + 2;
            }
        }
    }

    void ModuleAutoPlace::DrawText_(CG::Engine::AHUD* hud, CG::BasicTypes::FString text, float x, float y, uint8_t alignment, CG::CoreUObject::FLinearColor color, CG::Engine::UFont* font, float scale)
    {
        float width;
        float height;

        hud->GetTextSize(text, &width, &height, font, scale);

        const uint8_t CENTER = 1;
        const uint8_t RIGHT = 2;

        switch (alignment)
        {
        case CENTER:
            x -= width / 2;
            break;
        case RIGHT:
            x -= width;
            break;
        }

        CG::CoreUObject::FLinearColor outlineColor = { 0.f, 0.f, 0.f, 1.f };

        // Draw outline
        hud->DrawText(text, outlineColor, x - 1.0f, y, font, scale, false);
        hud->DrawText(text, outlineColor, x + 1.0f, y, font, scale, false);
        hud->DrawText(text, outlineColor, x, y - 1.0f, font, scale, false);
        hud->DrawText(text, outlineColor, x, y + 1.0f, font, scale, false);

        hud->DrawText(text, color, x, y, font, scale, false);
    }

    void ModuleAutoPlace::LoadSettings(nlohmann::json config)
    {
        Module::LoadSettings(config);
        m_enabled = GetCheckboxStateById(this->GetName(), 1, config);
        m_demolishDistance = GetSliderCurrentValueById(this->GetName(), 2, config);
        m_demolishOnlySelected = GetCheckboxStateById(this->GetName(), 3, config);
    }
}
