#pragma once
#include "pch.h"

namespace Base
{
    class ModuleAutoDemolish final : public Module
    {
    private:
        bool m_enabled = true;
        float m_demolishDistance = 2000.0f;
        bool m_demolishOnlySelected = false;

        // Helper functions adapted from ESP module
        void DrawTextY(CG::Engine::AHUD* hud, CG::BasicTypes::FString text, float x, float y, uint8_t alignment, CG::CoreUObject::FLinearColor color, CG::Engine::UFont* font, float scale, float* yValue);
        void DrawText_(CG::Engine::AHUD* hud, CG::BasicTypes::FString text, float x, float y, uint8_t alignment, CG::CoreUObject::FLinearColor color, CG::Engine::UFont* font, float scale);

    public:
        ModuleAutoDemolish(const std::string& name, const std::string& description, const int key_code, const Category category, const std::string& icon)
            : Module(name, description, key_code, category, icon) {}

        void SetupSettings() override;
        void LoadSettings(nlohmann::json config) override;
        void OnPostRender(CG::ShooterGame::UShooterGameViewportClient* viewport, CG::Engine::UCanvas* canvas, CG::Engine::UWorld* world, CG::Engine::UFont* font) override;
    };
}