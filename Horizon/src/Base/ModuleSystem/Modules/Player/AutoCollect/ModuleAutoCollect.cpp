#include "pch.h"
#include "ModuleAutoCollect.h"

#include "Renderer/Renderer.h"
#include "Renderer/Vector.h"

namespace Base
{
    void ModuleAutoCollect::SetupSettings()
    {
        AddSetting(new CheckboxSetting(XOR("Enabled"), m_enabled, 1));
        AddSetting(new SliderSetting(XOR("Demolish Distance"), m_demolishDistance, 0.0, 5000.0, 2));
        AddSetting(new CheckboxSetting(XOR("Demolish Only Selected Structure Type"), m_demolishOnlySelected, 3));
    }

    void ModuleAutoCollect::OnPostRender(CG::ShooterGame::UShooterGameViewportClient* viewport, CG::Engine::UCanvas* canvas, CG::Engine::UWorld* world, CG::Engine::UFont* font)
    {
        CG::ShooterGame::AShooterPlayerController* playerController = Global::GetPlayerController(world);
        if (playerController == nullptr || playerController->PlayerCameraManager == nullptr || !playerController->GetPlayerCharacter()) return;

        CG::ShooterGame::AShooterCharacter* selfPlayer = playerController->GetPlayerCharacter();
        if (selfPlayer == nullptr) return;

        CG::Engine::ULevel* persistentLevel = world->PersistentLevel;
        if (persistentLevel == nullptr || !persistentLevel->ActorsList.Count()) return;

        CG::BasicTypes::TArray<CG::Engine::AActor*> actorArray = persistentLevel->ActorsList;

        CG::Engine::AHUD* hud = playerController->GetShooterHud();
        if (!hud) return;

        hud->Canvas = canvas;

        // Static variables to track state
        static int currentIndex = 0;
        static CG::CoreUObject::UClass* currClass = nullptr;
        static int screenX = 0;
        static int screenY = 0;

        // Get viewport size once
        if (screenX == 0 && screenY == 0) {
            playerController->GetViewportSize(&screenX, &screenY);
        }

        // Display current selected structure
        std::wstring infoText;
        if (currClass) {
            infoText = XOR(L"[") + std::to_wstring(currentIndex) + XOR(L" / ") + Global::ConvertUtf8ToUtf162(currClass->GetName()) + XOR(L"] ");
        }
        else {
            infoText = XOR(L"[Auto Collect] ");
        }
        infoText += XOR(L"Collects Structures Nearby");

        // Draw current selection text
        float y = 175.0f;
        DrawTextY(hud, infoText.c_str(), screenX / 2.0f, y, 1, { 1.0f, 1.0f, 1.0f, 1.0f }, font, 1.0f, &y);

        // Rate limit execution
        static std::chrono::steady_clock::time_point lastExecutionTime = std::chrono::steady_clock::now();
        auto currentTime = std::chrono::steady_clock::now();
        auto elapsedTime = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - lastExecutionTime).count();
        if (elapsedTime < 100) return;
        lastExecutionTime = currentTime;

        for (int i = 0; i < actorArray.Count(); i++)
        {
            auto currentActor = actorArray[i];
            {
                if (currentActor == nullptr) continue;
                if (currentActor->RootComponent == nullptr) continue;
            }

            if (currentActor->IsA(CG::ShooterGame::APrimalStructure::StaticClass()))
            {
                auto structure = static_cast<CG::ShooterGame::APrimalStructure*>(currentActor);
                printf("Run4\n");
                if (structure->TargetingTeam == playerController->TargetingTeam && structure->CanPickupStructureFromRecentPlacement() && ((structure->GetDistanceTo(selfPlayer) / 100) < 30))
                {
                    printf("Run5\n");
                    playerController->ServerMultiUse(structure, 203);
                }
            }
        }
    }

    // Helper method for drawing text (adapted from ESP module)
    void ModuleAutoCollect::DrawTextY(CG::Engine::AHUD* hud, CG::BasicTypes::FString text, float x, float y, uint8_t alignment, CG::CoreUObject::FLinearColor color, CG::Engine::UFont* font, float scale, float* yValue)
    {
        float width;
        float height;

        CG::CoreUObject::FVector2D screenPos = { x, y };

        if (Renderer::IsInScreen(screenPos))
        {
            hud->GetTextSize(text, &width, &height, font, scale);

            if (text.IsValid() && text.ToString().length() != 0)
            {
                DrawText_(hud, text, x, y, alignment, color, font, scale);
                *yValue += height / 2 + 2;
            }
        }
    }

    void ModuleAutoCollect::DrawText_(CG::Engine::AHUD* hud, CG::BasicTypes::FString text, float x, float y, uint8_t alignment, CG::CoreUObject::FLinearColor color, CG::Engine::UFont* font, float scale)
    {
        float width;
        float height;

        hud->GetTextSize(text, &width, &height, font, scale);

        const uint8_t CENTER = 1;
        const uint8_t RIGHT = 2;

        switch (alignment)
        {
        case CENTER:
            x -= width / 2;
            break;
        case RIGHT:
            x -= width;
            break;
        }

        CG::CoreUObject::FLinearColor outlineColor = { 0.f, 0.f, 0.f, 1.f };

        // Draw outline
        hud->DrawText(text, outlineColor, x - 1.0f, y, font, scale, false);
        hud->DrawText(text, outlineColor, x + 1.0f, y, font, scale, false);
        hud->DrawText(text, outlineColor, x, y - 1.0f, font, scale, false);
        hud->DrawText(text, outlineColor, x, y + 1.0f, font, scale, false);

        hud->DrawText(text, color, x, y, font, scale, false);
    }

    void ModuleAutoCollect::LoadSettings(nlohmann::json config)
    {
        Module::LoadSettings(config);
        m_enabled = GetCheckboxStateById(this->GetName(), 1, config);
        m_demolishDistance = GetSliderCurrentValueById(this->GetName(), 2, config);
        m_demolishOnlySelected = GetCheckboxStateById(this->GetName(), 3, config);
    }
}
