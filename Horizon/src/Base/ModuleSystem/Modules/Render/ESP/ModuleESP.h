#pragma once

#include "pch.h"
#include <unordered_map>
#include <functional>

namespace Base
{
	/**
	 * @brief High-performance ESP module with clean architecture and FNameCache integration
	 */
	class ModuleESP final : public Module
	{
	public:
		ModuleESP(const std::string& name, const std::string& description, const int key_code, const Category category, const std::string& icon)
			: Module(name, description, key_code, category, icon) {}

		void SetupSettings() override;
		void LoadSettings(nlohmann::json config) override;
		void OnPostRender(CG::ShooterGame::UShooterGameViewportClient* viewport, CG::Engine::UCanvas* canvas, CG::Engine::UWorld* world, CG::Engine::UFont* font) override;

	private:
		// ===== CONFIGURATION STRUCTURES =====

		struct PlayerSettings
		{
			bool friendly = false;
			bool enemy = false;
			bool sleeping = false;
			bool dead = false;
			bool extendedInfo = false;
			bool hideHealth = false;
			bool hideTribe = false;
		};

		struct DinoSettings
		{
			bool friendly = false;
			bool enemy = false;
			bool wild = false;
			bool dead = false;
			bool alpha = false;
			bool levelFilter = false;
			bool hideLevel = false;
			bool hideAggression = false;
			int minLevel = 150;

			// Stat display
			bool showStamina = false;
			bool showOxygen = false;
			bool showFood = false;
			bool showWeight = false;
			bool showMeleeDamage = false;
			bool showMovementSpeed = false;
			bool showTorpor = false;
			bool nameFilter = false;
			std::array<std::string, 8> nameFilters = {};
		};

		struct StructureSettings
		{
			bool vault = false;
			bool dedicatedStorage = false;
			bool storageBoxSmall = false;
			bool storageBoxLarge = false;
			bool supplyCrate = false;
			bool turret = false;
			bool bed = false;
			bool tekGenerator = false;
			bool electricGenerator = false;
			bool cryoFridge = false;
			bool loadoutDummy = false;
			bool ammoBox = false;
			bool tekSensor = false;
			bool hideEmpty = false;
			bool hideItemAmount = false;
			bool hideHealth = true;
			bool hideTurretSettings = false;
			bool hideFriendly = true;
			bool hideEnemy = false;
		};

		struct ItemSettings
		{
			bool items = false;
			bool itemCache = false;
			bool eggs = false;
		};

		struct ResourceSettings
		{
			bool enabled = false;
			bool metal = true;
			bool oil = false;
			bool crystal = false;
			bool obsidian = false;
			bool silica = false;
			float renderDistance = 30000.0f;
			float reloadInterval = 5.0f;
		};

		struct MiscSettings
		{
			bool showSpawnpoints = false;
			bool explorerNotes = false;
			bool unlockedOnly = false;
			bool exRender = true;
		};

		struct Colors
		{
			CG::CoreUObject::FLinearColor structure = {1.f, 1.f, 1.f, 1.f};
			CG::CoreUObject::FLinearColor dino = {1.f, 1.f, 1.f, 1.f};
			CG::CoreUObject::FLinearColor enemyDino = {1.f, 1.f, 1.f, 1.f};
			CG::CoreUObject::FLinearColor player = {1.f, 1.f, 1.f, 1.f};
			CG::CoreUObject::FLinearColor enemyPlayer = {1.f, 1.f, 1.f, 1.f};
			CG::CoreUObject::FLinearColor health = {1.f, 1.f, 1.f, 1.f};
			CG::CoreUObject::FLinearColor dinoStats = {1.f, 1.f, 1.f, 1.f};
		};

		struct GeneralSettings
		{
			float textScale = 5.0f;
			int espRange = 1000;
		};

		// ===== SETTINGS INSTANCES =====
		PlayerSettings m_player;
		DinoSettings m_dino;
		StructureSettings m_structure;
		ItemSettings m_item;
		ResourceSettings m_resource;
		MiscSettings m_misc;
		Colors m_colors;
		GeneralSettings m_general;

		// ===== RENDERING CONTEXT =====
		struct RenderContext
		{
			CG::Engine::UFont* font;
			CG::ShooterGame::AShooterPlayerController* playerController;
			CG::Engine::AHUD* hud;
			CG::ShooterGame::AShooterCharacter* selfPlayer;
			CG::CoreUObject::FVector camLoc;
			CG::CoreUObject::FRotator camRot;
			float camFov;
		};

		// ===== CORE METHODS =====
		void ProcessActors(const RenderContext& ctx, CG::Engine::UWorld* world);
		void ProcessResourceESP(const RenderContext& ctx, CG::Engine::UWorld* world);
		void ProcessSpawnpoints(const RenderContext& ctx, CG::Engine::UWorld* world);
		void ProcessExplorerNotes(const RenderContext& ctx, CG::Engine::UWorld* world);

		// ===== ACTOR RENDERERS =====
		void RenderPlayer(const RenderContext& ctx, CG::ShooterGame::AShooterCharacter* player);
		void RenderDino(const RenderContext& ctx, CG::ShooterGame::APrimalDinoCharacter* dino);
		void RenderStructure(const RenderContext& ctx, CG::Engine::AActor* actor);
		void RenderItem(const RenderContext& ctx, CG::Engine::AActor* item);

		// ===== FILTERING LOGIC =====
		bool ShouldRender(const RenderContext& ctx, CG::Engine::AActor* actor) const;
		bool ShouldRenderPlayer(CG::ShooterGame::AShooterCharacter* player) const;
		bool ShouldRenderDino(CG::ShooterGame::APrimalDinoCharacter* dino) const;
		bool ShouldRenderStructure(CG::Engine::AActor* actor) const;
		bool PassesNameFilter(const std::string& dinoName) const;

		// ===== UTILITIES =====
		float GetDistance(const RenderContext& ctx, CG::Engine::AActor* actor) const;
		void DrawText(const RenderContext& ctx, const std::wstring& text, float x, float& y, const CG::CoreUObject::FLinearColor& color) const;
		void DrawTextY(CG::Engine::AHUD* hud, const std::wstring& text, float x, float y, uint8_t alignment, const CG::CoreUObject::FLinearColor& color, CG::Engine::UFont* font, float scale, float* yValue) const;

	};

}
