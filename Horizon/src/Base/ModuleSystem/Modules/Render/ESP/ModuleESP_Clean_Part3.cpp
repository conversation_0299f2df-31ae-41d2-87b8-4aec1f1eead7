// Part 3 of ModuleESP_Clean.cpp - Processing methods, utilities, and settings

namespace Base
{
	void ModuleESP::ProcessResourceESP(const RenderContext& ctx, CG::Engine::UWorld* world)
	{
		UpdateResourceCache(ctx, world);
		RenderCachedResources(ctx);
	}

	void ModuleESP::ProcessSpawnpoints(const RenderContext& ctx, CG::Engine::UWorld* world)
	{
		if (!world->PersistentLevel) return;

		auto actorArray = world->PersistentLevel->ActorsList;

		for (int i = 0; i < actorArray.Count(); ++i)
		{
			auto actor = actorArray[i];
			if (!actor) continue;

			// Check for spawnpoint actors using FNameCache
			if (FNameCache::IsA(actor, FNameCache::Actors::PrimalStructureBed))
			{
				auto screenPos = Renderer::WorldToScreen(actor->K2_GetActorLocation(), ctx.camLoc, ctx.camRot, ctx.camFov);
				if (!Renderer::IsInScreen(screenPos)) continue;

				float distance = GetDistance(ctx, actor);
				if (distance > 1000) continue; // Limit render distance

				std::wstring spawnName = L"Spawn Point [" + std::to_wstring(static_cast<int>(distance)) + L"m]";
				CG::CoreUObject::FLinearColor color = {0.0f, 1.0f, 0.0f, 1.0f}; // Green color

				float y = screenPos.Y;
				DrawTextY(ctx.hud, spawnName.c_str(), screenPos.X, y, 1, color, ctx.font, m_general.textScale, &y);
			}
		}
	}

	void ModuleESP::ProcessExplorerNotes(const RenderContext& ctx, CG::Engine::UWorld* world)
	{
		if (!world->PersistentLevel) return;

		auto actorArray = world->PersistentLevel->ActorsList;

		for (int i = 0; i < actorArray.Count(); ++i)
		{
			auto actor = actorArray[i];
			if (!actor) continue;

			if (FNameCache::IsA(actor, FNameCache::Actors::ExplorerChestBase))
			{
				auto screenPos = Renderer::WorldToScreen(actor->K2_GetActorLocation(), ctx.camLoc, ctx.camRot, ctx.camFov);
				if (!Renderer::IsInScreen(screenPos)) continue;

				float distance = GetDistance(ctx, actor);
				if (m_misc.exRender && distance > 500) continue; // Limit render distance

				std::wstring noteName = L"Explorer Note [" + std::to_wstring(static_cast<int>(distance)) + L"m]";
				CG::CoreUObject::FLinearColor color = {1.0f, 0.8f, 0.0f, 1.0f}; // Gold color

				float y = screenPos.Y;
				DrawTextY(ctx.hud, noteName.c_str(), screenPos.X, y, 1, color, ctx.font, m_general.textScale, &y);
			}
		}
	}

	void ModuleESP::ProcessBeacons(const RenderContext& ctx, CG::Engine::UWorld* world)
	{
		if (!world->PersistentLevel) return;

		auto actorArray = world->PersistentLevel->ActorsList;

		for (int i = 0; i < actorArray.Count(); ++i)
		{
			auto actor = actorArray[i];
			if (!actor) continue;

			// Check for beacon actors - you may need to add these to FNameCache
			// For now, using a generic check
			if (actor->Name.ToString().find("Beacon") != std::string::npos)
			{
				auto screenPos = Renderer::WorldToScreen(actor->K2_GetActorLocation(), ctx.camLoc, ctx.camRot, ctx.camFov);
				if (!Renderer::IsInScreen(screenPos)) continue;

				float distance = GetDistance(ctx, actor);
				if (distance > 2000) continue; // Limit render distance

				std::wstring beaconName = L"Beacon [" + std::to_wstring(static_cast<int>(distance)) + L"m]";
				CG::CoreUObject::FLinearColor color = {0.0f, 0.8f, 1.0f, 1.0f}; // Cyan color

				float y = screenPos.Y;
				DrawTextY(ctx.hud, beaconName.c_str(), screenPos.X, y, 1, color, ctx.font, m_general.textScale, &y);
			}
		}
	}

	void ModuleESP::ProcessObelisks(const RenderContext& ctx, CG::Engine::UWorld* world)
	{
		if (!world->PersistentLevel) return;

		auto actorArray = world->PersistentLevel->ActorsList;

		for (int i = 0; i < actorArray.Count(); ++i)
		{
			auto actor = actorArray[i];
			if (!actor) continue;

			// Check for obelisk actors - you may need to add these to FNameCache
			if (actor->Name.ToString().find("Obelisk") != std::string::npos)
			{
				auto screenPos = Renderer::WorldToScreen(actor->K2_GetActorLocation(), ctx.camLoc, ctx.camRot, ctx.camFov);
				if (!Renderer::IsInScreen(screenPos)) continue;

				float distance = GetDistance(ctx, actor);
				if (distance > 5000) continue; // Limit render distance

				std::wstring obeliskName = L"Obelisk [" + std::to_wstring(static_cast<int>(distance)) + L"m]";
				CG::CoreUObject::FLinearColor color = {1.0f, 0.0f, 1.0f, 1.0f}; // Magenta color

				float y = screenPos.Y;
				DrawTextY(ctx.hud, obeliskName.c_str(), screenPos.X, y, 1, color, ctx.font, m_general.textScale, &y);
			}
		}
	}

	void ModuleESP::UpdateResourceCache(const RenderContext& ctx, CG::Engine::UWorld* world)
	{
		auto currentTime = std::chrono::steady_clock::now();
		auto elapsedTime = std::chrono::duration_cast<std::chrono::seconds>(currentTime - s_lastResourceUpdate).count();

		if (elapsedTime > m_resource.reloadInterval)
		{
			auto victoryCore = Global::GetVictoryCore();
			if (!victoryCore) return;

			CG::CoreUObject::FVector origin = ctx.selfPlayer->K2_GetActorLocation();
			CG::BasicTypes::TArray<CG::ShooterGame::FOverlappedFoliageElement> foliage;
			victoryCore->STATIC_ServerSearchFoliage(world, origin, m_resource.renderDistance, &foliage, true, true, true, false, false);

			s_lastResourceUpdate = currentTime;
			s_cachedResources.clear();

			for (int i = 0; i < foliage.Count(); i++)
			{
				if (!foliage[i].HarvestingComponent) continue;

				std::wstring descName = foliage[i].HarvestingComponent->DescriptiveName.ToWString();
				
				// Filter resources based on settings
				bool shouldShow = false;
				if (m_resource.metal && descName.find(L"Metal") != std::wstring::npos) shouldShow = true;
				if (m_resource.oil && descName.find(L"Oil") != std::wstring::npos) shouldShow = true;
				if (m_resource.crystal && descName.find(L"Crystal") != std::wstring::npos) shouldShow = true;
				if (m_resource.obsidian && descName.find(L"Obsidian") != std::wstring::npos) shouldShow = true;
				if (m_resource.silica && descName.find(L"Silica") != std::wstring::npos) shouldShow = true;
				if (m_resource.element && descName.find(L"Element") != std::wstring::npos) shouldShow = true;
				if (m_resource.sulfur && descName.find(L"Sulfur") != std::wstring::npos) shouldShow = true;
				if (m_resource.polymer && descName.find(L"Polymer") != std::wstring::npos) shouldShow = true;

				if (shouldShow)
				{
					CachedResource cachedElement;
					cachedElement.name = descName;
					cachedElement.location = foliage[i].HarvestLocation;
					cachedElement.lastUpdate = currentTime;
					s_cachedResources.push_back(cachedElement);
				}
			}
		}
	}

	void ModuleESP::RenderCachedResources(const RenderContext& ctx)
	{
		CG::CoreUObject::FLinearColor color = {0.88f, 0.88f, 0.44f, 1.0f};
		
		for (const auto& resource : s_cachedResources)
		{
			if (resource.name.empty()) continue;

			float distance = Vector3(ctx.camLoc.X, ctx.camLoc.Y, ctx.camLoc.Z)
				.Distance(Vector3(resource.location.X, resource.location.Y, resource.location.Z)) / 100.0f;

			std::wstring resourceLabel = resource.name + L" [" + std::to_wstring(static_cast<int>(distance)) + L"m]";
			CG::CoreUObject::FVector2D screenPos = Renderer::WorldToScreen(resource.location, ctx.camLoc, ctx.camRot, ctx.camFov);

			if (Renderer::IsInScreen(screenPos))
			{
				float y = screenPos.Y;
				DrawTextY(ctx.hud, resourceLabel.c_str(), screenPos.X, y, 1, color, ctx.font, m_general.textScale, &y);
			}
		}
	}

	// ===== FILTERING LOGIC =====

	bool ModuleESP::ShouldRender(const RenderContext& ctx, CG::Engine::AActor* actor) const
	{
		if (!actor) return false;

		float distance = GetDistance(ctx, actor);
		return distance <= m_general.espRange;
	}

	bool ModuleESP::ShouldRenderPlayer(const RenderContext& ctx, CG::ShooterGame::AShooterCharacter* player) const
	{
		if (!player) return false;

		bool isAllied = player->TargetingTeam == ctx.playerController->TargetingTeam;
		bool isDead = player->IsDead();
		bool isSleeping = player->bIsSleeping;

		if (isAllied && !m_player.friendly) return false;
		if (!isAllied && !m_player.enemy) return false;
		if (isDead && !m_player.dead) return false;
		if (isSleeping && !m_player.sleeping) return false;

		return true;
	}

	bool ModuleESP::ShouldRenderDino(const RenderContext& ctx, CG::ShooterGame::APrimalDinoCharacter* dino) const
	{
		if (!dino) return false;

		bool isAllied = dino->TargetingTeam == ctx.playerController->TargetingTeam;
		bool isDead = dino->IsDead();
		bool isWild = !dino->BPIsTamed();
		bool isAlpha = dino->bIsAlpha;

		if (isAllied && !m_dino.friendly) return false;
		if (!isAllied && !isWild && !m_dino.enemy) return false;
		if (isWild && !m_dino.wild) return false;
		if (isDead && !m_dino.dead) return false;
		if (isAlpha && !m_dino.alpha) return false;

		// Level filter for wild dinos
		if (isWild && m_dino.levelFilter)
		{
			auto statusComp = dino->GetCharacterStatusComponent();
			if (statusComp && statusComp->GetCharacterLevel() < m_dino.minLevel)
				return false;
		}

		// Name filter
		if (m_dino.nameFilter && dino->DescriptiveName.IsValid())
		{
			return PassesNameFilter(dino->DescriptiveName.ToString());
		}

		return true;
	}

	bool ModuleESP::ShouldRenderStructure(const RenderContext& ctx, CG::Engine::AActor* actor) const
	{
		if (!actor) return false;

		bool isAllied = actor->TargetingTeam == ctx.playerController->TargetingTeam;

		if (isAllied && m_structure.hideFriendly) return false;
		if (!isAllied && m_structure.hideEnemy) return false;

		// Check specific structure types using FNameCache
		if (m_structure.vault && FNameCache::IsA(actor, FNameCache::Containers::StorageBoxHuge)) return true;
		if (m_structure.dedicatedStorage && FNameCache::IsA(actor, FNameCache::Containers::DedicatedStorage)) return true;
		if (m_structure.storageBoxSmall && FNameCache::IsA(actor, FNameCache::Containers::StorageBoxSmall)) return true;
		if (m_structure.storageBoxLarge && FNameCache::IsA(actor, FNameCache::Containers::StorageBoxLarge)) return true;
		if (m_structure.supplyCrate && FNameCache::IsA(actor, FNameCache::Actors::PrimalStructureItemContainerSupplyCrate)) return true;
		if (m_structure.turret && FNameCache::IsA(actor, FNameCache::Actors::PrimalStructureTurret)) return true;
		if (m_structure.bed && FNameCache::IsA(actor, FNameCache::Actors::PrimalStructureBed)) return true;
		if (m_structure.tekGenerator && FNameCache::IsA(actor, FNameCache::Containers::TekGenerator)) return true;
		if (m_structure.electricGenerator && FNameCache::IsA(actor, FNameCache::Containers::ElectricGenerator)) return true;
		if (m_structure.cryoFridge && FNameCache::IsA(actor, FNameCache::Containers::CryoFridge)) return true;
		if (m_structure.ammoBox && FNameCache::IsA(actor, FNameCache::Containers::AmmoContainer)) return true;

		return false;
	}

	bool ModuleESP::PassesNameFilter(const std::string& dinoName) const
	{
		if (!m_dino.nameFilter) return true;

		for (const auto& filter : m_dino.nameFilters)
		{
			if (!filter.empty() && dinoName.find(filter) != std::string::npos)
				return true;
		}
		return false;
	}

	// ===== UTILITIES =====

	float ModuleESP::GetDistance(const RenderContext& ctx, CG::Engine::AActor* actor) const
	{
		if (!actor) return FLT_MAX;

		auto actorLoc = actor->K2_GetActorLocation();
		return Vector3(ctx.camLoc.X, ctx.camLoc.Y, ctx.camLoc.Z)
			.Distance(Vector3(actorLoc.X, actorLoc.Y, actorLoc.Z)) / 100.0f;
	}

	void ModuleESP::DrawTextY(CG::Engine::AHUD* hud, const std::wstring& text, float x, float y, uint8_t alignment, 
							  const CG::CoreUObject::FLinearColor& color, CG::Engine::UFont* font, float scale, float* yValue) const
	{
		if (!hud || !font) return;

		// Implementation would call the actual HUD drawing function
		// This is a placeholder for the actual drawing implementation
		hud->DrawText(text.c_str(), color, x, y, font, scale, false);
		
		if (yValue)
			*yValue = y + (font->GetMaxCharHeight() * scale);
	}
}
