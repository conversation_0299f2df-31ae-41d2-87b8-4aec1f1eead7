// Part 2 of ModuleESP_Clean.cpp - Additional rendering methods and utilities

namespace Base
{
	void ModuleESP::RenderPlayer(const RenderContext& ctx, CG::ShooterGame::AShooterCharacter* player)
	{
		if (!player) return;

		auto screenPos = Renderer::WorldToScreen(player->K2_GetActorLocation(), ctx.camLoc, ctx.camRot, ctx.camFov);
		if (!Renderer::IsInScreen(screenPos)) return;

		// Get player info
		std::wstring playerName = player->PlayerName.IsValid() ? player->PlayerName.ToWString() : L"Unknown";
		std::wstring tribeName = player->TribeName.IsValid() ? player->TribeName.ToWString() : L"No Tribe";
		
		float distance = GetDistance(ctx, player);
		bool isAllied = player->TargetingTeam == ctx.playerController->TargetingTeam;
		bool isDead = player->IsDead();
		bool isSleeping = player->bIsSleeping;

		// Determine color
		CG::CoreUObject::FLinearColor color = isAllied ? m_colors.player : m_colors.enemyPlayer;
		if (isDead) color = {0.3f, 0.3f, 0.3f, 1.0f};

		float y = screenPos.Y;

		// Main player info
		auto level = player->GetCharacterStatusComponent() ? player->GetCharacterStatusComponent()->GetCharacterLevel() : 0;
		std::wstring mainText = playerName + L" - Lvl " + std::to_wstring(level) + L" [" + std::to_wstring(static_cast<int>(distance)) + L"m]";
		
		if (isSleeping) mainText += L" (Sleeping)";
		if (isDead) mainText += L" (Dead)";

		DrawTextY(ctx.hud, mainText.c_str(), screenPos.X, y, 1, color, ctx.font, m_general.textScale + 0.1f, &y);

		// Extended info
		if (m_player.extendedInfo)
		{
			// Tribe info for enemies
			if (!isAllied && !m_player.hideTribe)
			{
				DrawTextY(ctx.hud, tribeName.c_str(), screenPos.X, y, 1, m_colors.player, ctx.font, m_general.textScale, &y);
			}

			// Health info
			if (!m_player.hideHealth && !isDead)
			{
				float health = player->GetHealth();
				float maxHealth = player->GetMaxHealth();
				std::wstring healthText = L"HP: " + std::to_wstring(static_cast<int>(health)) + L"/" + std::to_wstring(static_cast<int>(maxHealth));
				DrawTextY(ctx.hud, healthText.c_str(), screenPos.X, y, 1, m_colors.health, ctx.font, m_general.textScale, &y);
			}

			// Platform and implant info
			std::wstring implantId = player->LinkedPlayerIDString().IsValid() ? player->LinkedPlayerIDString().ToWString() : L"-";
			
			if (!implantId.empty() && implantId != L"-")
			{
				DrawTextY(ctx.hud, (L"ID: " + implantId).c_str(), screenPos.X, y, 1, color, ctx.font, m_general.textScale - 0.5f, &y);
			}
		}
	}

	void ModuleESP::RenderDino(const RenderContext& ctx, CG::ShooterGame::APrimalDinoCharacter* dino)
	{
		if (!dino) return;

		auto screenPos = Renderer::WorldToScreen(dino->K2_GetActorLocation(), ctx.camLoc, ctx.camRot, ctx.camFov);
		if (!Renderer::IsInScreen(screenPos)) return;

		// Get basic info
		std::wstring dinoName = dino->DescriptiveName.IsValid() ? dino->DescriptiveName.ToWString() : L"Unknown";
		std::wstring tribeName = dino->TribeName.IsValid() ? dino->TribeName.ToWString() : L"No Tribe";

		float distance = GetDistance(ctx, dino);
		bool isAllied = dino->TargetingTeam == ctx.playerController->TargetingTeam;
		bool isTamed = dino->BPIsTamed();
		bool isDead = dino->IsDead();
		bool isAlpha = dino->bIsAlpha;

		// Get level
		auto statusComp = dino->GetCharacterStatusComponent();
		int level = statusComp ? statusComp->GetCharacterLevel() : 0;

		// Determine color
		CG::CoreUObject::FLinearColor color = isAllied ? m_colors.dino : m_colors.enemyDino;
		if (isDead) color = {0.3f, 0.3f, 0.3f, 1.0f};
		if (isAlpha) color = {1.0f, 0.0f, 1.0f, 1.0f}; // Purple for alpha

		float y = screenPos.Y;

		// Main dino info
		std::wstring mainText = dinoName;
		if (!m_dino.hideLevel) mainText += L" - Lvl " + std::to_wstring(level);
		mainText += L" [" + std::to_wstring(static_cast<int>(distance)) + L"m]";
		
		if (isAlpha) mainText += L" (Alpha)";
		if (isDead) mainText += L" (Dead)";
		if (!isTamed) mainText += L" (Wild)";

		DrawTextY(ctx.hud, mainText.c_str(), screenPos.X, y, 1, color, ctx.font, m_general.textScale, &y);

		// Health and aggression info
		if (!isDead)
		{
			std::wstring healthAggression;
			
			float health = dino->GetHealth();
			float maxHealth = dino->GetMaxHealth();
			healthAggression = std::to_wstring(static_cast<int>(health)) + L"/" + std::to_wstring(static_cast<int>(maxHealth)) + L" HP ";

			if (!m_dino.hideAggression && isTamed && dino->TamedAggressionLevel < 4)
			{
				std::string aggressionText[] = {"Passive", "Neutral", "Aggressive", "Attack YOUR Target"};
				healthAggression += L"[" + Global::ConvertUtf8ToUtf162(aggressionText[dino->TamedAggressionLevel]) + L"]";
			}

			if (!healthAggression.empty())
			{
				DrawTextY(ctx.hud, healthAggression.c_str(), screenPos.X, y, 1, m_colors.health, ctx.font, m_general.textScale - 0.5f, &y);
			}
		}

		// Tribe info for tamed dinos
		if (isTamed && !tribeName.empty() && tribeName != L"No Tribe")
		{
			DrawTextY(ctx.hud, tribeName.c_str(), screenPos.X, y, 1, color, ctx.font, m_general.textScale - 0.5f, &y);
		}

		// Dino stats (only for tamed or knocked out dinos)
		if (statusComp && (isTamed || dino->ReplicatedCurrentTorpor > 0))
		{
			std::wstring dinoStats;

			if (m_dino.showStamina)
				dinoStats += L"[Stamina: " + std::to_wstring(static_cast<int>(statusComp->BPGetCurrentStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::Stamina))) + L"/" + std::to_wstring(static_cast<int>(statusComp->BPGetMaxStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::Stamina))) + L"] ";

			if (m_dino.showOxygen)
				dinoStats += L"[Oxygen: " + std::to_wstring(static_cast<int>(statusComp->BPGetCurrentStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::Oxygen))) + L"/" + std::to_wstring(static_cast<int>(statusComp->BPGetMaxStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::Oxygen))) + L"] ";

			if (m_dino.showFood)
				dinoStats += L"[Food: " + std::to_wstring(static_cast<int>(statusComp->BPGetCurrentStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::Food))) + L"/" + std::to_wstring(static_cast<int>(statusComp->BPGetMaxStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::Food))) + L"] ";

			if (m_dino.showWeight)
				dinoStats += L"[Weight: " + std::to_wstring(static_cast<int>(statusComp->BPGetCurrentStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::Weight))) + L"/" + std::to_wstring(static_cast<int>(statusComp->BPGetMaxStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::Weight))) + L"] ";

			if (m_dino.showMeleeDamage)
				dinoStats += L"[Melee: " + std::to_wstring(static_cast<int>(statusComp->BPGetCurrentStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::MeleeDamageMultiplier) * 100)) + L"%] ";

			if (m_dino.showMovementSpeed)
				dinoStats += L"[Speed: " + std::to_wstring(static_cast<int>(statusComp->BPGetCurrentStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::MovementSpeedMultiplier) * 100)) + L"%] ";

			if (m_dino.showTorpor)
				dinoStats += L"[Torpor: " + std::to_wstring(static_cast<int>(statusComp->BPGetCurrentStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::Torpidity))) + L"/" + std::to_wstring(static_cast<int>(statusComp->BPGetMaxStatusValue(CG::ShooterGame::EPrimalCharacterStatusValue::Torpidity))) + L"] ";

			if (!dinoStats.empty())
			{
				DrawTextY(ctx.hud, dinoStats.c_str(), screenPos.X, y, 1, m_colors.dinoStats, ctx.font, m_general.textScale - 1.0f, &y);
			}
		}
	}

	void ModuleESP::RenderStructure(const RenderContext& ctx, CG::Engine::AActor* actor)
	{
		if (!actor) return;

		auto screenPos = Renderer::WorldToScreen(actor->K2_GetActorLocation(), ctx.camLoc, ctx.camRot, ctx.camFov);
		if (!Renderer::IsInScreen(screenPos)) return;

		std::wstring structureName = L"Structure";
		float distance = GetDistance(ctx, actor);
		bool isAllied = actor->TargetingTeam == ctx.playerController->TargetingTeam;

		// Determine specific structure type using FNameCache
		if (FNameCache::IsA(actor, FNameCache::Containers::StorageBoxHuge))
			structureName = L"Vault";
		else if (FNameCache::IsA(actor, FNameCache::Containers::DedicatedStorage))
			structureName = L"Dedicated Storage";
		else if (FNameCache::IsA(actor, FNameCache::Containers::StorageBoxSmall))
			structureName = L"Small Storage";
		else if (FNameCache::IsA(actor, FNameCache::Containers::StorageBoxLarge))
			structureName = L"Large Storage";
		else if (FNameCache::IsA(actor, FNameCache::Actors::PrimalStructureItemContainerSupplyCrate))
			structureName = L"Supply Crate";
		else if (FNameCache::IsA(actor, FNameCache::Actors::PrimalStructureTurret))
			structureName = L"Turret";
		else if (FNameCache::IsA(actor, FNameCache::Actors::PrimalStructureBed))
			structureName = L"Bed";
		else if (FNameCache::IsA(actor, FNameCache::Containers::TekGenerator))
			structureName = L"Tek Generator";
		else if (FNameCache::IsA(actor, FNameCache::Containers::ElectricGenerator))
			structureName = L"Electric Generator";
		else if (FNameCache::IsA(actor, FNameCache::Containers::CryoFridge))
			structureName = L"Cryo Fridge";
		else if (FNameCache::IsA(actor, FNameCache::Containers::AmmoContainer))
			structureName = L"Ammo Box";

		// Determine color
		CG::CoreUObject::FLinearColor color = m_colors.structure;
		if (!isAllied) color = {1.0f, 0.5f, 0.5f, 1.0f}; // Reddish for enemy structures

		float y = screenPos.Y;

		// Main structure info
		std::wstring mainText = structureName + L" [" + std::to_wstring(static_cast<int>(distance)) + L"m]";
		DrawTextY(ctx.hud, mainText.c_str(), screenPos.X, y, 1, color, ctx.font, m_general.textScale, &y);

		// Additional structure info
		if (!m_structure.hideHealth)
		{
			float health = actor->GetHealth();
			float maxHealth = actor->GetMaxHealth();
			if (health > 0 && maxHealth > 0)
			{
				std::wstring healthText = L"HP: " + std::to_wstring(static_cast<int>(health)) + L"/" + std::to_wstring(static_cast<int>(maxHealth));
				DrawTextY(ctx.hud, healthText.c_str(), screenPos.X, y, 1, m_colors.health, ctx.font, m_general.textScale - 0.5f, &y);
			}
		}
	}

	void ModuleESP::RenderItem(const RenderContext& ctx, CG::Engine::AActor* item)
	{
		if (!item) return;

		auto screenPos = Renderer::WorldToScreen(item->K2_GetActorLocation(), ctx.camLoc, ctx.camRot, ctx.camFov);
		if (!Renderer::IsInScreen(screenPos)) return;

		std::wstring itemName = L"Item";
		float distance = GetDistance(ctx, item);

		// Determine specific item type using FNameCache
		if (FNameCache::IsA(item, FNameCache::Actors::DroppedItemEgg))
			itemName = L"Egg";
		else if (FNameCache::IsA(item, FNameCache::Actors::DroppedItemGeneric))
			itemName = L"Dropped Item";
		else if (FNameCache::IsA(item, FNameCache::Containers::DeathItemCache))
			itemName = L"Death Cache";

		CG::CoreUObject::FLinearColor color = {1.0f, 1.0f, 0.0f, 1.0f}; // Yellow for items

		float y = screenPos.Y;
		std::wstring mainText = itemName + L" [" + std::to_wstring(static_cast<int>(distance)) + L"m]";
		DrawTextY(ctx.hud, mainText.c_str(), screenPos.X, y, 1, color, ctx.font, m_general.textScale, &y);
	}
}
