// Part 4 of ModuleESP_Clean.cpp - Settings loading methods

namespace Base
{
	// ===== SETTINGS HELPERS =====

	void ModuleESP::LoadPlayerSettings(const nlohmann::json& config)
	{
		m_player.friendly = GetCheckboxStateById(this->GetName(), 10, config);
		m_player.enemy = GetCheckboxStateById(this->GetName(), 11, config);
		m_player.sleeping = GetCheckboxStateById(this->GetName(), 12, config);
		m_player.dead = GetCheckboxStateById(this->GetName(), 13, config);
		m_player.extendedInfo = GetCheckboxStateById(this->GetName(), 14, config);
		m_player.hideHealth = GetCheckboxStateById(this->GetName(), 15, config);
		m_player.hideTribe = GetCheckboxStateById(this->GetN<PERSON>(), 16, config);
	}

	void ModuleESP::LoadDinoSettings(const nlohmann::json& config)
	{
		m_dino.friendly = GetCheckboxStateById(this->GetName(), 20, config);
		m_dino.enemy = GetCheckboxStateById(this->GetName(), 21, config);
		m_dino.wild = GetCheckboxStateById(this->GetName(), 22, config);
		m_dino.dead = GetCheckboxStateById(this->GetName(), 23, config);
		m_dino.alpha = GetCheckboxStateById(this->GetName(), 24, config);
		m_dino.levelFilter = GetCheckboxStateById(this->GetName(), 25, config);
		m_dino.minLevel = static_cast<int>(GetSliderValueById(this->GetName(), 26, config));
		m_dino.hideLevel = GetCheckboxStateById(this->GetName(), 27, config);
		m_dino.hideAggression = GetCheckboxStateById(this->GetName(), 28, config);
		m_dino.nameFilter = GetCheckboxStateById(this->GetName(), 29, config);

		// Dino stats
		m_dino.showStamina = GetCheckboxStateById(this->GetName(), 30, config);
		m_dino.showOxygen = GetCheckboxStateById(this->GetName(), 31, config);
		m_dino.showFood = GetCheckboxStateById(this->GetName(), 32, config);
		m_dino.showWeight = GetCheckboxStateById(this->GetName(), 33, config);
		m_dino.showMeleeDamage = GetCheckboxStateById(this->GetName(), 34, config);
		m_dino.showMovementSpeed = GetCheckboxStateById(this->GetName(), 35, config);
		m_dino.showTorpor = GetCheckboxStateById(this->GetName(), 36, config);

		// Name filters
		for (int i = 0; i < 8; ++i)
		{
			m_dino.nameFilters[i] = GetTextboxValueById(this->GetName(), i + 1, config);
		}
	}

	void ModuleESP::LoadStructureSettings(const nlohmann::json& config)
	{
		m_structure.vault = GetCheckboxStateById(this->GetName(), 40, config);
		m_structure.dedicatedStorage = GetCheckboxStateById(this->GetName(), 41, config);
		m_structure.storageBoxSmall = GetCheckboxStateById(this->GetName(), 42, config);
		m_structure.storageBoxLarge = GetCheckboxStateById(this->GetName(), 43, config);
		m_structure.supplyCrate = GetCheckboxStateById(this->GetName(), 44, config);
		m_structure.turret = GetCheckboxStateById(this->GetName(), 45, config);
		m_structure.bed = GetCheckboxStateById(this->GetName(), 46, config);
		m_structure.tekGenerator = GetCheckboxStateById(this->GetName(), 47, config);
		m_structure.electricGenerator = GetCheckboxStateById(this->GetName(), 48, config);
		m_structure.cryoFridge = GetCheckboxStateById(this->GetName(), 49, config);
		m_structure.loadoutDummy = GetCheckboxStateById(this->GetName(), 50, config);
		m_structure.ammoBox = GetCheckboxStateById(this->GetName(), 51, config);
		m_structure.tekSensor = GetCheckboxStateById(this->GetName(), 52, config);
		m_structure.hideEmpty = GetCheckboxStateById(this->GetName(), 53, config);
		m_structure.hideItemAmount = GetCheckboxStateById(this->GetName(), 54, config);
		m_structure.hideHealth = GetCheckboxStateById(this->GetName(), 55, config);
		m_structure.hideTurretSettings = GetCheckboxStateById(this->GetName(), 56, config);
		m_structure.hideFriendly = GetCheckboxStateById(this->GetName(), 57, config);
		m_structure.hideEnemy = GetCheckboxStateById(this->GetName(), 58, config);
	}

	void ModuleESP::LoadItemSettings(const nlohmann::json& config)
	{
		m_item.items = GetCheckboxStateById(this->GetName(), 60, config);
		m_item.itemCache = GetCheckboxStateById(this->GetName(), 61, config);
		m_item.eggs = GetCheckboxStateById(this->GetName(), 62, config);
	}

	void ModuleESP::LoadResourceSettings(const nlohmann::json& config)
	{
		m_resource.enabled = GetCheckboxStateById(this->GetName(), 70, config);
		m_resource.metal = GetCheckboxStateById(this->GetName(), 71, config);
		m_resource.oil = GetCheckboxStateById(this->GetName(), 72, config);
		m_resource.crystal = GetCheckboxStateById(this->GetName(), 73, config);
		m_resource.obsidian = GetCheckboxStateById(this->GetName(), 74, config);
		m_resource.silica = GetCheckboxStateById(this->GetName(), 75, config);
		m_resource.element = GetCheckboxStateById(this->GetName(), 76, config);
		m_resource.sulfur = GetCheckboxStateById(this->GetName(), 77, config);
		m_resource.polymer = GetCheckboxStateById(this->GetName(), 78, config);
		m_resource.renderDistance = GetSliderValueById(this->GetName(), 79, config);
		m_resource.reloadInterval = GetSliderValueById(this->GetName(), 80, config);
	}

	void ModuleESP::LoadMiscSettings(const nlohmann::json& config)
	{
		m_misc.showSpawnpoints = GetCheckboxStateById(this->GetName(), 90, config);
		m_misc.explorerNotes = GetCheckboxStateById(this->GetName(), 91, config);
		m_misc.unlockedOnly = GetCheckboxStateById(this->GetName(), 92, config);
		m_misc.exRender = GetCheckboxStateById(this->GetName(), 93, config);
		m_misc.showBeacons = GetCheckboxStateById(this->GetName(), 94, config);
		m_misc.showObelisks = GetCheckboxStateById(this->GetName(), 95, config);
	}

	void ModuleESP::LoadColorSettings(const nlohmann::json& config)
	{
		// Load color settings from config
		// This would typically involve parsing hex color values and converting to FLinearColor
		// For now, using default values
		
		auto structureColor = GetColorValueById(this->GetName(), 1, config);
		if (structureColor.has_value())
			m_colors.structure = structureColor.value();

		auto friendlyDinoColor = GetColorValueById(this->GetName(), 2, config);
		if (friendlyDinoColor.has_value())
			m_colors.dino = friendlyDinoColor.value();

		auto enemyDinoColor = GetColorValueById(this->GetName(), 3, config);
		if (enemyDinoColor.has_value())
			m_colors.enemyDino = enemyDinoColor.value();

		auto friendlyPlayerColor = GetColorValueById(this->GetName(), 4, config);
		if (friendlyPlayerColor.has_value())
			m_colors.player = friendlyPlayerColor.value();

		auto enemyPlayerColor = GetColorValueById(this->GetName(), 5, config);
		if (enemyPlayerColor.has_value())
			m_colors.enemyPlayer = enemyPlayerColor.value();

		auto healthColor = GetColorValueById(this->GetName(), 6, config);
		if (healthColor.has_value())
			m_colors.health = healthColor.value();

		auto dinoStatsColor = GetColorValueById(this->GetName(), 7, config);
		if (dinoStatsColor.has_value())
			m_colors.dinoStats = dinoStatsColor.value();
	}

	void ModuleESP::LoadGeneralSettings(const nlohmann::json& config)
	{
		m_general.textScale = GetSliderValueById(this->GetName(), 1, config);
		m_general.espRange = static_cast<int>(GetSliderValueById(this->GetName(), 2, config));
	}

	// Helper method to get color value from config
	std::optional<CG::CoreUObject::FLinearColor> ModuleESP::GetColorValueById(const std::string& moduleName, int id, const nlohmann::json& config) const
	{
		try
		{
			if (config.contains("modules") && config["modules"].contains(moduleName))
			{
				auto moduleConfig = config["modules"][moduleName];
				if (moduleConfig.contains("colors"))
				{
					auto colors = moduleConfig["colors"];
					for (const auto& colorSetting : colors)
					{
						if (colorSetting.contains("id") && colorSetting["id"] == id)
						{
							if (colorSetting.contains("value"))
							{
								std::string hexValue = colorSetting["value"];
								// Parse hex color and convert to FLinearColor
								// This is a simplified implementation
								if (hexValue.length() >= 7 && hexValue[0] == '#')
								{
									unsigned int r, g, b;
									sscanf_s(hexValue.substr(1, 2).c_str(), "%x", &r);
									sscanf_s(hexValue.substr(3, 2).c_str(), "%x", &g);
									sscanf_s(hexValue.substr(5, 2).c_str(), "%x", &b);
									
									return CG::CoreUObject::FLinearColor{
										r / 255.0f,
										g / 255.0f,
										b / 255.0f,
										1.0f
									};
								}
							}
						}
					}
				}
			}
		}
		catch (...)
		{
			// Return nullopt if parsing fails
		}
		
		return std::nullopt;
	}

	// Helper method to get textbox value from config
	std::string ModuleESP::GetTextboxValueById(const std::string& moduleName, int id, const nlohmann::json& config) const
	{
		try
		{
			if (config.contains("modules") && config["modules"].contains(moduleName))
			{
				auto moduleConfig = config["modules"][moduleName];
				if (moduleConfig.contains("textboxes"))
				{
					auto textboxes = moduleConfig["textboxes"];
					for (const auto& textboxSetting : textboxes)
					{
						if (textboxSetting.contains("id") && textboxSetting["id"] == id)
						{
							if (textboxSetting.contains("value"))
							{
								return textboxSetting["value"];
							}
						}
					}
				}
			}
		}
		catch (...)
		{
			// Return empty string if parsing fails
		}
		
		return "";
	}
}
