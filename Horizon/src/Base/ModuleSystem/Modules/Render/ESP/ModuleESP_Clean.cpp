#include "pch.h"
#include "ModuleESP_Clean.h"
#include "Renderer/Renderer.h"
#include <codecvt>

namespace Base
{
	// Static member initialization
	std::vector<ModuleESP::CachedResource> ModuleESP::s_cachedResources;
	std::chrono::steady_clock::time_point ModuleESP::s_lastResourceUpdate = std::chrono::steady_clock::now();

	void ModuleESP::SetupSettings()
	{
		// ===== GENERAL SETTINGS =====
		AddSetting(new SliderSetting(XOR("Text Size"), m_general.textScale, 1.0, 10.0, 1));
		AddSetting(new SliderSetting(XOR("ESP Range"), m_general.espRange, 0, 1000, 2));

		// ===== PLAYER SETTINGS =====
		AddSetting(new CheckboxSetting(XOR("Friendly Players"), m_player.friendly, 10));
		AddSetting(new CheckboxSetting(XOR("Enemy Players"), m_player.enemy, 11));
		AddSetting(new CheckboxSetting(XOR("Sleeping Players"), m_player.sleeping, 12));
		AddSetting(new CheckboxSetting(XOR("Dead Players"), m_player.dead, 13));
		AddSetting(new CheckboxSetting(XOR("Extended Player Info"), m_player.extendedInfo, 14));
		AddSetting(new CheckboxSetting(XOR("Hide Player Health"), m_player.hideHealth, 15));
		AddSetting(new CheckboxSetting(XOR("Hide Player Tribe"), m_player.hideTribe, 16));

		// ===== DINO SETTINGS =====
		AddSetting(new CheckboxSetting(XOR("Friendly Dinos"), m_dino.friendly, 20));
		AddSetting(new CheckboxSetting(XOR("Enemy Dinos"), m_dino.enemy, 21));
		AddSetting(new CheckboxSetting(XOR("Wild Dinos"), m_dino.wild, 22));
		AddSetting(new CheckboxSetting(XOR("Dead Dinos"), m_dino.dead, 23));
		AddSetting(new CheckboxSetting(XOR("Alpha Dinos"), m_dino.alpha, 24));
		AddSetting(new CheckboxSetting(XOR("Level Filter"), m_dino.levelFilter, 25));
		AddSetting(new SliderSetting(XOR("Min Level"), m_dino.minLevel, 1, 500, 26));
		AddSetting(new CheckboxSetting(XOR("Hide Dino Level"), m_dino.hideLevel, 27));
		AddSetting(new CheckboxSetting(XOR("Hide Aggression"), m_dino.hideAggression, 28));
		AddSetting(new CheckboxSetting(XOR("Name Filter"), m_dino.nameFilter, 29));

		// ===== DINO STATS =====
		AddSetting(new CheckboxSetting(XOR("Show Stamina"), m_dino.showStamina, 30));
		AddSetting(new CheckboxSetting(XOR("Show Oxygen"), m_dino.showOxygen, 31));
		AddSetting(new CheckboxSetting(XOR("Show Food"), m_dino.showFood, 32));
		AddSetting(new CheckboxSetting(XOR("Show Weight"), m_dino.showWeight, 33));
		AddSetting(new CheckboxSetting(XOR("Show Melee Damage"), m_dino.showMeleeDamage, 34));
		AddSetting(new CheckboxSetting(XOR("Show Movement Speed"), m_dino.showMovementSpeed, 35));
		AddSetting(new CheckboxSetting(XOR("Show Torpor"), m_dino.showTorpor, 36));

		// ===== STRUCTURE SETTINGS =====
		AddSetting(new CheckboxSetting(XOR("Vaults"), m_structure.vault, 40));
		AddSetting(new CheckboxSetting(XOR("Dedicated Storage"), m_structure.dedicatedStorage, 41));
		AddSetting(new CheckboxSetting(XOR("Small Storage Box"), m_structure.storageBoxSmall, 42));
		AddSetting(new CheckboxSetting(XOR("Large Storage Box"), m_structure.storageBoxLarge, 43));
		AddSetting(new CheckboxSetting(XOR("Supply Crates"), m_structure.supplyCrate, 44));
		AddSetting(new CheckboxSetting(XOR("Turrets"), m_structure.turret, 45));
		AddSetting(new CheckboxSetting(XOR("Beds"), m_structure.bed, 46));
		AddSetting(new CheckboxSetting(XOR("Tek Generator"), m_structure.tekGenerator, 47));
		AddSetting(new CheckboxSetting(XOR("Electric Generator"), m_structure.electricGenerator, 48));
		AddSetting(new CheckboxSetting(XOR("Cryo Fridge"), m_structure.cryoFridge, 49));
		AddSetting(new CheckboxSetting(XOR("Loadout Dummy"), m_structure.loadoutDummy, 50));
		AddSetting(new CheckboxSetting(XOR("Ammo Box"), m_structure.ammoBox, 51));
		AddSetting(new CheckboxSetting(XOR("Tek Sensor"), m_structure.tekSensor, 52));
		AddSetting(new CheckboxSetting(XOR("Hide Empty Containers"), m_structure.hideEmpty, 53));
		AddSetting(new CheckboxSetting(XOR("Hide Item Amount"), m_structure.hideItemAmount, 54));
		AddSetting(new CheckboxSetting(XOR("Hide Structure Health"), m_structure.hideHealth, 55));
		AddSetting(new CheckboxSetting(XOR("Hide Turret Settings"), m_structure.hideTurretSettings, 56));
		AddSetting(new CheckboxSetting(XOR("Hide Friendly Structures"), m_structure.hideFriendly, 57));
		AddSetting(new CheckboxSetting(XOR("Hide Enemy Structures"), m_structure.hideEnemy, 58));

		// ===== ITEM SETTINGS =====
		AddSetting(new CheckboxSetting(XOR("Items"), m_item.items, 60));
		AddSetting(new CheckboxSetting(XOR("Item Cache"), m_item.itemCache, 61));
		AddSetting(new CheckboxSetting(XOR("Eggs"), m_item.eggs, 62));

		// ===== RESOURCE ESP =====
		AddSetting(new CheckboxSetting(XOR("Enable Resource ESP"), m_resource.enabled, 70));
		AddSetting(new CheckboxSetting(XOR("Show Metal"), m_resource.metal, 71));
		AddSetting(new CheckboxSetting(XOR("Show Oil"), m_resource.oil, 72));
		AddSetting(new CheckboxSetting(XOR("Show Crystal"), m_resource.crystal, 73));
		AddSetting(new CheckboxSetting(XOR("Show Obsidian"), m_resource.obsidian, 74));
		AddSetting(new CheckboxSetting(XOR("Show Silica"), m_resource.silica, 75));
		AddSetting(new CheckboxSetting(XOR("Show Element"), m_resource.element, 76));
		AddSetting(new CheckboxSetting(XOR("Show Sulfur"), m_resource.sulfur, 77));
		AddSetting(new CheckboxSetting(XOR("Show Polymer"), m_resource.polymer, 78));
		AddSetting(new SliderSetting(XOR("Resource Render Distance"), m_resource.renderDistance, 0.0, 150000.0, 79));
		AddSetting(new SliderSetting(XOR("Resource Refresh Rate (Seconds)"), m_resource.reloadInterval, 1.0, 60.0, 80));

		// ===== MISC SETTINGS =====
		AddSetting(new CheckboxSetting(XOR("Player Spawnpoints"), m_misc.showSpawnpoints, 90));
		AddSetting(new CheckboxSetting(XOR("Explorer Notes"), m_misc.explorerNotes, 91));
		AddSetting(new CheckboxSetting(XOR("Unlocked Only"), m_misc.unlockedOnly, 92));
		AddSetting(new CheckboxSetting(XOR("Limit Render Distance"), m_misc.exRender, 93));
		AddSetting(new CheckboxSetting(XOR("Show Beacons"), m_misc.showBeacons, 94));
		AddSetting(new CheckboxSetting(XOR("Show Obelisks"), m_misc.showObelisks, 95));

		// ===== COLORS =====
		AddSetting(new ColorsSetting(XOR("Structure Color"), XOR("#9900ff"), 1));
		AddSetting(new ColorsSetting(XOR("Friendly Dino Color"), XOR("#0099cc"), 2));
		AddSetting(new ColorsSetting(XOR("Enemy Dino Color"), XOR("#9900cc"), 3));
		AddSetting(new ColorsSetting(XOR("Friendly Player Color"), XOR("#0066ff"), 4));
		AddSetting(new ColorsSetting(XOR("Enemy Player Color"), XOR("#6600ff"), 5));
		AddSetting(new ColorsSetting(XOR("Health Color"), XOR("#ff3300"), 6));
		AddSetting(new ColorsSetting(XOR("Dino Stats Color"), XOR("#006aff"), 7));

		// ===== DINO NAME FILTERS =====
		for (int i = 0; i < 8; ++i)
		{
			AddSetting(new TextboxSetting(XOR("Dino Name Filter ") + std::to_string(i + 1), "", i + 1));
		}
	}

	void ModuleESP::LoadSettings(nlohmann::json config)
	{
		Module::LoadSettings(config);
		
		LoadPlayerSettings(config);
		LoadDinoSettings(config);
		LoadStructureSettings(config);
		LoadItemSettings(config);
		LoadResourceSettings(config);
		LoadMiscSettings(config);
		LoadColorSettings(config);
		LoadGeneralSettings(config);
	}

	void ModuleESP::OnPostRender(CG::ShooterGame::UShooterGameViewportClient* viewport, CG::Engine::UCanvas* canvas, 
								 CG::Engine::UWorld* world, CG::Engine::UFont* font)
	{
		if (!IsEnabled()) return;

		RenderContext ctx;
		InitializeRenderContext(ctx, viewport, canvas, world, font);

		if (!ctx.playerController || !ctx.selfPlayer || !ctx.hud) return;

		// Process different ESP types
		ProcessActors(ctx, world);

		if (m_resource.enabled)
			ProcessResourceESP(ctx, world);

		if (m_misc.showSpawnpoints)
			ProcessSpawnpoints(ctx, world);

		if (m_misc.explorerNotes)
			ProcessExplorerNotes(ctx, world);

		if (m_misc.showBeacons)
			ProcessBeacons(ctx, world);

		if (m_misc.showObelisks)
			ProcessObelisks(ctx, world);
	}

	void ModuleESP::InitializeRenderContext(RenderContext& ctx, CG::ShooterGame::UShooterGameViewportClient* viewport, 
											CG::Engine::UCanvas* canvas, CG::Engine::UWorld* world, CG::Engine::UFont* font)
	{
		ctx.font = font;
		ctx.playerController = Global::GetPlayerController(world);

		if (!ctx.playerController || !ctx.playerController->PlayerCameraManager || !ctx.playerController->GetPlayerCharacter())
			return;

		ctx.selfPlayer = ctx.playerController->GetPlayerCharacter();
		ctx.hud = ctx.playerController->GetShooterHud();

		if (!ctx.hud || !world->PersistentLevel || !world->PersistentLevel->ActorsList.Count())
			return;

		ctx.hud->Canvas = canvas;
		ctx.camLoc = ctx.playerController->PlayerCameraManager->GetCameraLocation();
		ctx.camRot = ctx.playerController->PlayerCameraManager->GetCameraRotation();
		ctx.camFov = ctx.playerController->PlayerCameraManager->CameraCachePrivate.POV.FOV;
	}

	void ModuleESP::ProcessActors(const RenderContext& ctx, CG::Engine::UWorld* world)
	{
		if (!world->PersistentLevel) return;

		auto actorArray = world->PersistentLevel->ActorsList;

		for (int i = 0; i < actorArray.Count(); ++i)
		{
			auto actor = actorArray[i];
			if (!actor || !ShouldRender(ctx, actor))
				continue;

			// Use FNameCache for efficient type checking
			if (FNameCache::IsA(actor, FNameCache::Actors::ShooterCharacter))
			{
				auto player = static_cast<CG::ShooterGame::AShooterCharacter*>(actor);
				if (ShouldRenderPlayer(ctx, player))
					RenderPlayer(ctx, player);
			}
			else if (FNameCache::IsA(actor, FNameCache::Actors::PrimalDinoCharacter))
			{
				auto dino = static_cast<CG::ShooterGame::APrimalDinoCharacter*>(actor);
				if (ShouldRenderDino(ctx, dino))
					RenderDino(ctx, dino);
			}
			else if (FNameCache::IsA(actor, FNameCache::Actors::PrimalStructure))
			{
				if (ShouldRenderStructure(ctx, actor))
					RenderStructure(ctx, actor);
			}
			else if (m_item.items && (FNameCache::IsA(actor, FNameCache::Actors::DroppedItemEgg) ||
									  FNameCache::IsA(actor, FNameCache::Actors::DroppedItemGeneric) ||
									  FNameCache::IsA(actor, FNameCache::Containers::DeathItemCache)))
			{
				RenderItem(ctx, actor);
			}
		}
	}
}
