#pragma once

#include "pch.h"
#include <unordered_map>
#include <functional>
#include <chrono>

namespace Base
{
	/**
	 * @brief High-performance ESP module with clean architecture and FNameCache integration
	 * 
	 * Features:
	 * - Modular rendering system with type-specific handlers
	 * - Efficient FNameCache-based actor filtering
	 * - Clean configuration management
	 * - Optimized rendering pipeline with caching
	 * - Complete settings implementation
	 */
	class ModuleESP final : public Module
	{
	public:
		ModuleESP(const std::string& name, const std::string& description, const int key_code, 
				  const Category category, const std::string& icon)
			: Module(name, description, key_code, category, icon) {}

		void SetupSettings() override;
		void LoadSettings(nlohmann::json config) override;
		void OnPostRender(CG::ShooterGame::UShooterGameViewportClient* viewport, CG::Engine::UCanvas* canvas, 
						  CG::Engine::UWorld* world, CG::Engine::UFont* font) override;

	private:
		// ===== CONFIGURATION STRUCTURES =====

		struct PlayerSettings
		{
			bool friendly = false;
			bool enemy = false;
			bool sleeping = false;
			bool dead = false;
			bool extendedInfo = false;
			bool hideHealth = false;
			bool hideTribe = false;
		};

		struct DinoSettings
		{
			bool friendly = false;
			bool enemy = false;
			bool wild = false;
			bool dead = false;
			bool alpha = false;
			bool levelFilter = false;
			bool hideLevel = false;
			bool hideAggression = false;
			int minLevel = 150;

			// Stat display options
			bool showStamina = false;
			bool showOxygen = false;
			bool showFood = false;
			bool showWeight = false;
			bool showMeleeDamage = false;
			bool showMovementSpeed = false;
			bool showTorpor = false;
			bool nameFilter = false;
			std::array<std::string, 8> nameFilters = {};
		};

		struct StructureSettings
		{
			bool vault = false;
			bool dedicatedStorage = false;
			bool storageBoxSmall = false;
			bool storageBoxLarge = false;
			bool supplyCrate = false;
			bool turret = false;
			bool bed = false;
			bool tekGenerator = false;
			bool electricGenerator = false;
			bool cryoFridge = false;
			bool loadoutDummy = false;
			bool ammoBox = false;
			bool tekSensor = false;
			bool hideEmpty = false;
			bool hideItemAmount = false;
			bool hideHealth = true;
			bool hideTurretSettings = false;
			bool hideFriendly = true;
			bool hideEnemy = false;
		};

		struct ItemSettings
		{
			bool items = false;
			bool itemCache = false;
			bool eggs = false;
		};

		struct ResourceSettings
		{
			bool enabled = false;
			bool metal = true;
			bool oil = false;
			bool crystal = false;
			bool obsidian = false;
			bool silica = false;
			bool element = false;
			bool sulfur = false;
			bool polymer = false;
			float renderDistance = 30000.0f;
			float reloadInterval = 5.0f;
		};

		struct MiscSettings
		{
			bool showSpawnpoints = false;
			bool explorerNotes = false;
			bool unlockedOnly = false;
			bool exRender = true;
			bool showBeacons = false;
			bool showObelisks = false;
		};

		struct Colors
		{
			CG::CoreUObject::FLinearColor structure = {1.f, 1.f, 1.f, 1.f};
			CG::CoreUObject::FLinearColor dino = {1.f, 1.f, 1.f, 1.f};
			CG::CoreUObject::FLinearColor enemyDino = {1.f, 1.f, 1.f, 1.f};
			CG::CoreUObject::FLinearColor player = {1.f, 1.f, 1.f, 1.f};
			CG::CoreUObject::FLinearColor enemyPlayer = {1.f, 1.f, 1.f, 1.f};
			CG::CoreUObject::FLinearColor health = {1.f, 1.f, 1.f, 1.f};
			CG::CoreUObject::FLinearColor dinoStats = {1.f, 1.f, 1.f, 1.f};
		};

		struct GeneralSettings
		{
			float textScale = 5.0f;
			int espRange = 1000;
		};

		// ===== SETTINGS INSTANCES =====
		PlayerSettings m_player;
		DinoSettings m_dino;
		StructureSettings m_structure;
		ItemSettings m_item;
		ResourceSettings m_resource;
		MiscSettings m_misc;
		Colors m_colors;
		GeneralSettings m_general;

		// ===== RENDERING CONTEXT =====
		struct RenderContext
		{
			CG::Engine::UFont* font;
			CG::ShooterGame::AShooterPlayerController* playerController;
			CG::Engine::AHUD* hud;
			CG::ShooterGame::AShooterCharacter* selfPlayer;
			CG::CoreUObject::FVector camLoc;
			CG::CoreUObject::FRotator camRot;
			float camFov;
		};

		// ===== CACHED RESOURCE STRUCTURE =====
		struct CachedResource
		{
			std::wstring name;
			CG::CoreUObject::FVector location;
			std::chrono::steady_clock::time_point lastUpdate;
		};

		// ===== CORE METHODS =====
		void InitializeRenderContext(RenderContext& ctx, CG::ShooterGame::UShooterGameViewportClient* viewport, 
									 CG::Engine::UCanvas* canvas, CG::Engine::UWorld* world, CG::Engine::UFont* font);
		void ProcessActors(const RenderContext& ctx, CG::Engine::UWorld* world);
		void ProcessResourceESP(const RenderContext& ctx, CG::Engine::UWorld* world);
		void ProcessSpawnpoints(const RenderContext& ctx, CG::Engine::UWorld* world);
		void ProcessExplorerNotes(const RenderContext& ctx, CG::Engine::UWorld* world);
		void ProcessBeacons(const RenderContext& ctx, CG::Engine::UWorld* world);
		void ProcessObelisks(const RenderContext& ctx, CG::Engine::UWorld* world);

		// ===== ACTOR RENDERERS =====
		void RenderPlayer(const RenderContext& ctx, CG::ShooterGame::AShooterCharacter* player);
		void RenderDino(const RenderContext& ctx, CG::ShooterGame::APrimalDinoCharacter* dino);
		void RenderStructure(const RenderContext& ctx, CG::Engine::AActor* actor);
		void RenderItem(const RenderContext& ctx, CG::Engine::AActor* item);

		// ===== FILTERING LOGIC =====
		bool ShouldRender(const RenderContext& ctx, CG::Engine::AActor* actor) const;
		bool ShouldRenderPlayer(const RenderContext& ctx, CG::ShooterGame::AShooterCharacter* player) const;
		bool ShouldRenderDino(const RenderContext& ctx, CG::ShooterGame::APrimalDinoCharacter* dino) const;
		bool ShouldRenderStructure(const RenderContext& ctx, CG::Engine::AActor* actor) const;
		bool PassesNameFilter(const std::string& dinoName) const;

		// ===== UTILITIES =====
		float GetDistance(const RenderContext& ctx, CG::Engine::AActor* actor) const;
		void DrawText(const RenderContext& ctx, const std::wstring& text, float x, float& y, 
					  const CG::CoreUObject::FLinearColor& color) const;
		void DrawTextY(CG::Engine::AHUD* hud, const std::wstring& text, float x, float y, uint8_t alignment, 
					   const CG::CoreUObject::FLinearColor& color, CG::Engine::UFont* font, float scale, float* yValue) const;

		// ===== RESOURCE CACHING =====
		static std::vector<CachedResource> s_cachedResources;
		static std::chrono::steady_clock::time_point s_lastResourceUpdate;
		void UpdateResourceCache(const RenderContext& ctx, CG::Engine::UWorld* world);
		void RenderCachedResources(const RenderContext& ctx);

		// ===== SETTINGS HELPERS =====
		void LoadPlayerSettings(const nlohmann::json& config);
		void LoadDinoSettings(const nlohmann::json& config);
		void LoadStructureSettings(const nlohmann::json& config);
		void LoadItemSettings(const nlohmann::json& config);
		void LoadResourceSettings(const nlohmann::json& config);
		void LoadMiscSettings(const nlohmann::json& config);
		void LoadColorSettings(const nlohmann::json& config);
		void LoadGeneralSettings(const nlohmann::json& config);
	};
}
