#pragma once

#include "pch.h"
#include <unordered_map>
#include <functional>

namespace Base
{
    /**
     * @brief High-performance ESP module with clean architecture and FNameCache integration
     * 
     * Features:
     * - Modular rendering system with type-specific handlers
     * - Efficient FNameCache-based actor filtering
     * - Clean configuration management
     * - Optimized rendering pipeline
     */
    class ModuleESP final : public Module
    {
    public:
        ModuleESP(const std::string& name, const std::string& description, const int key_code, 
                  const Category category, const std::string& icon) 
            : Module(name, description, key_code, category, icon) {}

        void SetupSettings() override;
        void LoadSettings(nlohmann::json config) override;
        void OnPostRender(CG::ShooterGame::UShooterGameViewportClient* viewport, CG::Engine::UCanvas* canvas, 
                         CG::Engine::UWorld* world, CG::Engine::UFont* font) override;

    private:
        // ===== CONFIGURATION STRUCTURES =====
        
        struct PlayerConfig
        {
            bool showFriendly = false;
            bool showEnemy = false;
            bool showSleeping = false;
            bool showDead = false;
            bool showExtendedInfo = false;
            bool hideHealth = false;
            bool hideTribe = false;
            CG::CoreUObject::FLinearColor friendlyColor = {0.0f, 1.0f, 0.0f, 1.0f};
            CG::CoreUObject::FLinearColor enemyColor = {1.0f, 0.0f, 0.0f, 1.0f};
            CG::CoreUObject::FLinearColor healthColor = {1.0f, 1.0f, 1.0f, 1.0f};
        };

        struct DinoConfig
        {
            bool showFriendly = false;
            bool showEnemy = false;
            bool showWild = false;
            bool showDead = false;
            bool showAlpha = false;
            bool levelFilter = false;
            bool hideLevel = false;
            bool hideAggression = false;
            int minLevel = 150;
            CG::CoreUObject::FLinearColor friendlyColor = {0.0f, 1.0f, 0.0f, 1.0f};
            CG::CoreUObject::FLinearColor enemyColor = {1.0f, 0.0f, 0.0f, 1.0f};
            CG::CoreUObject::FLinearColor wildColor = {1.0f, 1.0f, 0.0f, 1.0f};
            CG::CoreUObject::FLinearColor statsColor = {0.8f, 0.8f, 0.8f, 1.0f};
            
            // Stat display options
            struct Stats {
                bool stamina = false;
                bool oxygen = false;
                bool food = false;
                bool weight = false;
                bool meleeDamage = false;
                bool movementSpeed = false;
                bool torpor = false;
            } stats;
        };

        struct StructureConfig
        {
            bool showTurrets = false;
            bool showBeds = false;
            bool showGenerators = false;
            bool showContainers = false;
            bool showSupplyCrates = false;
            bool hideEmpty = false;
            bool hideItemAmount = false;
            bool hideHealth = true;
            bool hideTurretSettings = false;
            bool hideFriendly = true;
            bool hideEnemy = false;
            CG::CoreUObject::FLinearColor color = {1.0f, 1.0f, 1.0f, 1.0f};
        };

        struct ResourceConfig
        {
            bool enabled = false;
            bool metal = true;
            bool oil = false;
            bool crystal = false;
            bool obsidian = false;
            bool silica = false;
            float renderDistance = 30000.0f;
            float reloadInterval = 5.0f;
        };

        struct GeneralConfig
        {
            float textScale = 5.0f;
            int espRange = 1000;
            bool showSpawnpoints = false;
            bool showExplorerNotes = false;
            bool showUnlockedOnly = false;
        };

        // ===== CONFIGURATION INSTANCES =====
        PlayerConfig m_playerConfig;
        DinoConfig m_dinoConfig;
        StructureConfig m_structureConfig;
        ResourceConfig m_resourceConfig;
        GeneralConfig m_generalConfig;

        // ===== RENDERING CONTEXT =====
        struct RenderContext
        {
            CG::Engine::UFont* font;
            CG::ShooterGame::AShooterPlayerController* playerController;
            CG::Engine::AHUD* hud;
            CG::ShooterGame::AShooterCharacter* selfPlayer;
            CG::CoreUObject::FVector cameraLocation;
            CG::CoreUObject::FRotator cameraRotation;
            float cameraFOV;
        };

        // ===== CORE RENDERING METHODS =====
        void InitializeRenderContext(RenderContext& ctx, CG::ShooterGame::UShooterGameViewportClient* viewport, 
                                    CG::Engine::UCanvas* canvas, CG::Engine::UWorld* world, CG::Engine::UFont* font);
        
        void ProcessActors(const RenderContext& ctx, CG::Engine::UWorld* world);
        void ProcessResourceESP(const RenderContext& ctx, CG::Engine::UWorld* world);
        void ProcessSpawnpoints(const RenderContext& ctx, CG::Engine::UWorld* world);
        void ProcessExplorerNotes(const RenderContext& ctx, CG::Engine::UWorld* world);

        // ===== ACTOR-SPECIFIC RENDERERS =====
        void RenderPlayer(const RenderContext& ctx, CG::ShooterGame::AShooterCharacter* player);
        void RenderDino(const RenderContext& ctx, CG::ShooterGame::APrimalDinoCharacter* dino);
        void RenderStructure(const RenderContext& ctx, CG::Engine::AActor* structure);
        void RenderContainer(const RenderContext& ctx, CG::Engine::AActor* container);
        void RenderTurret(const RenderContext& ctx, CG::ShooterGame::APrimalStructureTurret* turret);

        // ===== UTILITY METHODS =====
        bool ShouldRenderActor(const RenderContext& ctx, CG::Engine::AActor* actor) const;
        float GetDistanceToActor(const RenderContext& ctx, CG::Engine::AActor* actor) const;
        CG::CoreUObject::FVector2D WorldToScreen(const RenderContext& ctx, const CG::CoreUObject::FVector& worldPos) const;
        
        void DrawText(const RenderContext& ctx, const std::wstring& text, float x, float& y, 
                     const CG::CoreUObject::FLinearColor& color) const;
        void DrawHealthBar(const RenderContext& ctx, float x, float& y, float health, float maxHealth) const;
        
        // ===== CONFIGURATION HELPERS =====
        void LoadPlayerConfig(const nlohmann::json& config);
        void LoadDinoConfig(const nlohmann::json& config);
        void LoadStructureConfig(const nlohmann::json& config);
        void LoadResourceConfig(const nlohmann::json& config);
        void LoadGeneralConfig(const nlohmann::json& config);
    };
}
