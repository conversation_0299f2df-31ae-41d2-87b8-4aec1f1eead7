#include "pch.h"
#include "ModuleHUD.h"
#include <codecvt>

#include "ModuleSystem/ModuleManager/ModuleManager.h"

namespace Base
{
    const uint8_t LEFT = 0;
    const uint8_t CENTER = 1;
    const uint8_t RIGHT = 2;

    void ModuleHUD::SetupSettings()
    {
        AddSetting(new CheckboxSetting(XOR("Show Server FPS"), m_serverFps, 1));
        AddSetting(new CheckboxSetting(XOR("Show Upload Timer"), m_timer, 2));
        AddSetting(new CheckboxSetting(XOR("Show Connected Players"), m_playerConnected, 3));
        AddSetting(new CheckboxSetting(XOR("Show Tamed Dinos"), m_tamedDinos, 4));
        AddSetting(new CheckboxSetting(XOR("Show Servername"), m_servername, 5));
        AddSetting(new CheckboxSetting(XOR("Show Mesh Detection"), m_mesh, 6));
        AddSetting(new CheckboxSetting(XOR("Show Enabled Modules"), m_moduleList, 7));
        AddSetting(new CheckboxSetting(XOR("Show Server Time"), m_clock, 8));
        AddSetting(new CheckboxSetting(XOR("Show Coordinates"), m_coordinates, 9));
        AddSetting(new CheckboxSetting(XOR("Show Aimbot Bone"), m_aimBone, 10));
        AddSetting(new CheckboxSetting(XOR("Show Player Kill Counter"), m_killCounterPlayers, 11));
        AddSetting(new CheckboxSetting(XOR("Show Dino Kill Counter"), m_killCounterDinos, 12));
        AddSetting(new CheckboxSetting(XOR("Show Death Counter"), m_deathCounter, 13));
        AddSetting(new CheckboxSetting(XOR("Show KD (Kill/Death Ratio)"), m_kd, 14));
        AddSetting(new CheckboxSetting(XOR("Show Hexagons"), m_hexagons, 15));
        AddSetting(new CheckboxSetting(XOR("Show Enemy Players Nearby"), m_playersNearby, 16));
        AddSetting(new CheckboxSetting(XOR("Disable Rainbow Color"), m_disableRainbow, 17));
        AddSetting(new CheckboxSetting(XOR("Show Reconnect Timer"), m_reconnect, 18));

        AddSetting(new ColorsSetting(XOR("HUD Color"), XOR("#FFFFFF"), 1));

        // AddSetting(new SliderSetting(XOR(L"Text Size")), m_textScale, 1.0, 10.0, 1));
    }

    void ModuleHUD::DrawText_(CG::Engine::AHUD* hud, CG::BasicTypes::FString text, float x, float y, uint8_t alignment, CG::CoreUObject::FLinearColor color, CG::Engine::UFont* font, float scale)
    {
        float width;
        float height;

        hud->GetTextSize(text, &width, &height, font, scale);

        switch (alignment)
        {
        case CENTER:
            x -= width / 2;
            break;
        case RIGHT:
            x -= width;
            break;
        }

        CG::CoreUObject::FLinearColor outlineColor = { 0.f, 0.f, 0.f, 1.f };

        // Draw outline
        hud->DrawText(text, outlineColor, x - 1.0f, y, font, scale, false);
        hud->DrawText(text, outlineColor, x + 1.0f, y, font, scale, false);
        hud->DrawText(text, outlineColor, x, y - 1.0f, font, scale, false);
        hud->DrawText(text, outlineColor, x, y + 1.0f, font, scale, false);

        hud->DrawText(text, color, x, y, font, scale, false);
        //hud->Canvas->K2_DrawText(font, text, { x, y }, color, 0.f, { 0.f, 0.f, 0.f, 0.f }, { 0.f, 0.f }, false, false, true, { 0.f, 0.f, 0.f, 0.f });
    }

    void ModuleHUD::DrawTextY(CG::Engine::AHUD* HUD, CG::BasicTypes::FString text, float x, float y, uint8_t alignment, CG::CoreUObject::FLinearColor color, CG::Engine::UFont* font, float scale, float* yValue)
    {
        float width;
        float height;

        HUD->GetTextSize(text, &width, &height, font, scale);

        if (text.IsValid() && text.ToString().length() != 0)
        {
            DrawText_(HUD, text, x, y, alignment, color, font, scale);
            *yValue += height / 2 + 2;
        }
    }

    void ModuleHUD::OnPostRender(CG::ShooterGame::UShooterGameViewportClient* viewport, CG::Engine::UCanvas* canvas, CG::Engine::UWorld* world, CG::Engine::UFont* font)
    {
        auto playerController = Global::GetPlayerController(world);

        int index = 0;

        std::wstring title = Global::ConvertUtf8ToUtf162(XOR("Horizon"));
        CG::CoreUObject::FVector2D textSize = canvas->K2_TextSize(font, title.c_str(), CG::CoreUObject::FVector2D(1.f, 1.f));

        float textHeight = textSize.Y;
        float xPos = 5.f;
        float yPos = (index * (textHeight)+5.f);

        CG::CoreUObject::FLinearColor titleColor = Global::SkyRainbow(index, 1.f, 10.f);

        if (m_disableRainbow)
        {
            titleColor = m_color;
        }
        
    	canvas->K2_DrawText(font, title.c_str(), { xPos, yPos }, CG::CoreUObject::FVector2D(1.0f, 1.0f), titleColor, 0.f, { 0.f, 0.f, 0.f, 0.f }, { 0.f, 0.f }, false, false, false, { 0.f, 0.f, 0.f, 0.f });
        index += 1;

        if (world->GameState && world->GameState->IsA(CG::ShooterGame::AShooterGameState::StaticClass()))
        {
            auto shooterGameState = static_cast<CG::ShooterGame::AShooterGameState*>(world->GameState);

            if (m_serverFps)
            {
                std::wstring serverFps = Global::ConvertUtf8ToUtf162(XOR("Server FPS: ")) + std::to_wstring(static_cast<int>(shooterGameState->ServerFramerate));

                CG::CoreUObject::FVector2D textSize = canvas->K2_TextSize(font, serverFps.c_str(), CG::CoreUObject::FVector2D(1.f, 1.f));

                float textHeight = textSize.Y;
                float xPos = 5.f;
                float yPos = (index * (textHeight)+5.f);

                CG::CoreUObject::FLinearColor color = Global::SkyRainbow(index, 1.f, 10.f);

                if (m_disableRainbow)
                {
                    color = m_color;
                }

                canvas->K2_DrawText(font, serverFps.c_str(), { xPos, yPos }, CG::CoreUObject::FVector2D(1.0f, 1.0f), color, 0.f, { 0.f, 0.f, 0.f, 0.f }, { 0.f, 0.f }, false, false, false, { 0.f, 0.f, 0.f, 0.f });
                index += 1;
            }

            if (m_reconnect)
            {
                std::wstring reconnect = L"0"; // Global::GetReconnectTimeString();

                CG::CoreUObject::FVector2D textSize = canvas->K2_TextSize(font, reconnect.c_str(), CG::CoreUObject::FVector2D(1.f, 1.f));

                float textHeight = textSize.Y;
                float xPos = 5.f;
                float yPos = (index * (textHeight)+5.f);

                CG::CoreUObject::FLinearColor color = Global::SkyRainbow(index, 1.f, 10.f);

                if (m_disableRainbow)
                {
                    color = m_color;
                }

                canvas->K2_DrawText(font, reconnect.c_str(), { xPos, yPos }, CG::CoreUObject::FVector2D(1.0f, 1.0f), color, 0.f, { 0.f, 0.f, 0.f, 0.f }, { 0.f, 0.f }, false, false, false, { 0.f, 0.f, 0.f, 0.f });
                index += 1;
            }

            if (m_coordinates && playerController && playerController->AcknowledgedPawn != nullptr)
            {
                CG::CoreUObject::FVector location = playerController->AcknowledgedPawn->K2_GetActorLocation();
                CG::CoreUObject::FIntPoint gps = reinterpret_cast<CG::ShooterGame::UVictoryCore*>(playerController)->STATIC_CalculateGPSCoordinates(world, location);
                // std::wstring coordinates = XOR(L"X: ") + std::to_wstring(gps.X) + XOR(L", Y: ") + std::to_wstring(gps.Y);

                std::wstringstream wss;
                wss.precision(2);
                wss << std::fixed << Global::ConvertUtf8ToUtf162(XOR("Lat: ")) << (gps.Y / 10) << Global::ConvertUtf8ToUtf162(XOR(", Lon: ")) << (gps.X / 10);
                std::wstring coordinates = wss.str();

                CG::CoreUObject::FVector2D textSize = canvas->K2_TextSize(font, coordinates.c_str(), CG::CoreUObject::FVector2D(1.f, 1.f));

                float textHeight = textSize.Y;
                float xPos = 5.f;
                float yPos = (index * (textHeight)+5.f);

                CG::CoreUObject::FLinearColor color = Global::SkyRainbow(index, 1.f, 10.f);

                if (m_disableRainbow)
                {
                    color = m_color;
                }

                canvas->K2_DrawText(font, coordinates.c_str(), { xPos, yPos }, CG::CoreUObject::FVector2D(1.0f, 1.0f), color, 0.f, { 0.f, 0.f, 0.f, 0.f }, { 0.f, 0.f }, false, false, false, { 0.f, 0.f, 0.f, 0.f });
                index += 1;
            }

            if (m_servername && shooterGameState->ServerSessionName.IsValid())
            {
                std::wstring serverName = (Global::ConvertUtf8ToUtf162(XOR("Server Name: ")) + shooterGameState->ServerSessionName.ToWString()).c_str();

                CG::CoreUObject::FVector2D textSize = canvas->K2_TextSize(font, serverName.c_str(), CG::CoreUObject::FVector2D(1.f, 1.f));

                float textHeight = textSize.Y;
                float xPos = 5.f;
                float yPos = (index * (textHeight)+5.f);

                CG::CoreUObject::FLinearColor color = Global::SkyRainbow(index, 1.f, 10.f);

                if (m_disableRainbow)
                {
                    color = m_color;
                }

                canvas->K2_DrawText(font, serverName.c_str(), { xPos, yPos }, CG::CoreUObject::FVector2D(1.0f, 1.0f), color, 0.f, { 0.f, 0.f, 0.f, 0.f }, { 0.f, 0.f }, false, false, false, { 0.f, 0.f, 0.f, 0.f });
                index += 1;
            }

            if (m_aimBone)
            {
                std::wstring aimBone = (Global::ConvertUtf8ToUtf162(XOR("Aimbot Bone: ")) + Global::ActiveBone).c_str();

                CG::CoreUObject::FVector2D textSize = canvas->K2_TextSize(font, aimBone.c_str(), CG::CoreUObject::FVector2D(1.f, 1.f));

                float textHeight = textSize.Y;
                float xPos = 5.f;
                float yPos = (index * (textHeight)+5.f);

                CG::CoreUObject::FLinearColor color = Global::SkyRainbow(index, 1.f, 10.f);

                if (m_disableRainbow)
                {
                    color = m_color;
                }

                canvas->K2_DrawText(font, aimBone.c_str(), { xPos, yPos }, CG::CoreUObject::FVector2D(1.0f, 1.0f), color, 0.f, { 0.f, 0.f, 0.f, 0.f }, { 0.f, 0.f }, false, false, false, { 0.f, 0.f, 0.f, 0.f });
                index += 1;
            }

            if (m_timer)
            {
                auto timer = shooterGameState->ServerSaveInterval - (shooterGameState->NetworkTime - shooterGameState->LastServerSaveTime);
                auto timerStr = reinterpret_cast<CG::ShooterGame::UVictoryCore*>(shooterGameState)->STATIC_FormatAsTime(static_cast<int>(timer), true, false, true);
                CG::CoreUObject::FVector2D textSize = canvas->K2_TextSize(font, timerStr, CG::CoreUObject::FVector2D(1.f, 1.f));

                float textHeight = textSize.Y;
                float xPos = 5.f;
                float yPos = (index * (textHeight)+5.f);

                CG::CoreUObject::FLinearColor color = Global::SkyRainbow(index, 1.f, 10.f);

                if (m_disableRainbow)
                {
                    color = m_color;
                }

                canvas->K2_DrawText(font, (Global::ConvertUtf8ToUtf162(XOR("Timer: ")) + timerStr.ToWString()).c_str(), { xPos, yPos }, CG::CoreUObject::FVector2D(1.0f, 1.0f), color, 0.f, { 0.f, 0.f, 0.f, 0.f }, { 0.f, 0.f }, false, false, false, { 0.f, 0.f, 0.f, 0.f });
                index += 1;
            }

            if (m_mesh && playerController)
            {
                if (playerController->AcknowledgedPawn != nullptr && playerController->AcknowledgedPawn->IsA(CG::ShooterGame::APrimalCharacter::StaticClass()))
                {
                    bool meshDetection = reinterpret_cast<CG::ShooterGame::UVictoryCore*>(playerController)->STATIC_IsUndermesh(static_cast<CG::ShooterGame::APrimalCharacter*>(playerController->AcknowledgedPawn), NULL, NULL, NULL, false, 0.f);

                    std::wstring meshText = Global::ConvertUtf8ToUtf162(XOR("Mesh Detection: "));

                    if (meshDetection)
                    {
                        meshText += Global::ConvertUtf8ToUtf162(XOR("Inside Mesh"));
                    }
                    else
                    {
                        meshText += Global::ConvertUtf8ToUtf162(XOR("Outside Mesh"));
                    }

                    CG::CoreUObject::FVector2D textSize = canvas->K2_TextSize(font, meshText.c_str(), CG::CoreUObject::FVector2D(1.f, 1.f));

                    float textHeight = textSize.Y;
                    float xPos = 5.f;
                    float yPos = (index * (textHeight)+5.f);

                    CG::CoreUObject::FLinearColor color = Global::SkyRainbow(index, 1.f, 10.f);

                    if (m_disableRainbow)
                    {
                        color = m_color;
                    }

                    canvas->K2_DrawText(font, meshText.c_str(), { xPos, yPos }, CG::CoreUObject::FVector2D(1.0f, 1.0f), color, 0.f, { 0.f, 0.f, 0.f, 0.f }, { 0.f, 0.f }, false, false, false, { 0.f, 0.f, 0.f, 0.f });
                    index += 1;
                }
            }

            if (m_killCounterPlayers)
            {
                std::wstring killCounter = (Global::ConvertUtf8ToUtf162(XOR("Player Kills: ")) + std::to_wstring(Global::KillCounterPlayers));
                CG::CoreUObject::FVector2D textSize = canvas->K2_TextSize(font, killCounter.c_str(), CG::CoreUObject::FVector2D(1.f, 1.f));

                float textHeight = textSize.Y;
                float xPos = 5.f;
                float yPos = (index * (textHeight)+5.f);

                CG::CoreUObject::FLinearColor color = Global::SkyRainbow(index, 1.f, 10.f);

                if (m_disableRainbow)
                {
                    color = m_color;
                }

                canvas->K2_DrawText(font, killCounter.c_str(), { xPos, yPos }, CG::CoreUObject::FVector2D(1.0f, 1.0f), color, 0.f, { 0.f, 0.f, 0.f, 0.f }, { 0.f, 0.f }, false, false, false, { 0.f, 0.f, 0.f, 0.f });
                index += 1;
            }

            if (m_killCounterDinos)
            {
                std::wstring killCounter = (Global::ConvertUtf8ToUtf162(XOR("Dino Kills: ")) + std::to_wstring(Global::KillCounterDinos));
                CG::CoreUObject::FVector2D textSize = canvas->K2_TextSize(font, killCounter.c_str(), CG::CoreUObject::FVector2D(1.f, 1.f));

                float textHeight = textSize.Y;
                float xPos = 5.f;
                float yPos = (index * (textHeight)+5.f);

                CG::CoreUObject::FLinearColor color = Global::SkyRainbow(index, 1.f, 10.f);

                if (m_disableRainbow)
                {
                    color = m_color;
                }

                canvas->K2_DrawText(font, killCounter.c_str(), { xPos, yPos }, CG::CoreUObject::FVector2D(1.0f, 1.0f), color, 0.f, { 0.f, 0.f, 0.f, 0.f }, { 0.f, 0.f }, false, false, false, { 0.f, 0.f, 0.f, 0.f });
                index += 1;
            }

            if (m_deathCounter)
            {
                std::wstring killCounter = (Global::ConvertUtf8ToUtf162(XOR("Deaths: ")) + std::to_wstring(Global::DeathCounter));
                CG::CoreUObject::FVector2D textSize = canvas->K2_TextSize(font, killCounter.c_str(), CG::CoreUObject::FVector2D(1.f, 1.f));

                float textHeight = textSize.Y;
                float xPos = 5.f;
                float yPos = (index * (textHeight)+5.f);

                CG::CoreUObject::FLinearColor color = Global::SkyRainbow(index, 1.f, 10.f);

                if (m_disableRainbow)
                {
                    color = m_color;
                }

                canvas->K2_DrawText(font, killCounter.c_str(), { xPos, yPos }, CG::CoreUObject::FVector2D(1.0f, 1.0f), color, 0.f, { 0.f, 0.f, 0.f, 0.f }, { 0.f, 0.f }, false, false, false, { 0.f, 0.f, 0.f, 0.f });
                index += 1;
            }

            if (m_kd)
            {
                float kd = 0;

                if (Global::DeathCounter > 0)
                {
                    kd = static_cast<float>(Global::KillCounterPlayers + Global::KillCounterDinos) / Global::DeathCounter;
                }

                std::wstringstream wss;
                wss << std::fixed << std::setprecision(2);

                std::wstring killCounter;

                if (Global::DeathCounter != 0)
                {
                    wss << Global::ConvertUtf8ToUtf162(XOR("KD: ")) << kd;
                    killCounter = wss.str();
                }
                else
                {
                    killCounter = Global::ConvertUtf8ToUtf162(XOR("KD: Infinity"));
                }

                CG::CoreUObject::FVector2D textSize = canvas->K2_TextSize(font, killCounter.c_str(), CG::CoreUObject::FVector2D(1.f, 1.f));

                float textHeight = textSize.Y;
                float xPos = 5.f;
                float yPos = (index * (textHeight)+5.f);

                CG::CoreUObject::FLinearColor color = Global::SkyRainbow(index, 1.f, 10.f);

                if (m_disableRainbow)
                {
                    color = m_color;
                }

                canvas->K2_DrawText(font, killCounter.c_str(), { xPos, yPos }, CG::CoreUObject::FVector2D(1.0f, 1.0f), color, 0.f, { 0.f, 0.f, 0.f, 0.f }, { 0.f, 0.f }, false, false, false, { 0.f, 0.f, 0.f, 0.f });
                index += 1;
            }

            if (m_playerConnected)
            {
                std::wstring playerConnected = (Global::ConvertUtf8ToUtf162(XOR("Players Connected: ")) + std::to_wstring(shooterGameState->NumPlayerConnected)) + Global::ConvertUtf8ToUtf162(XOR(" - ")) + std::to_wstring(shooterGameState->PlayerArray.Count());

                CG::CoreUObject::FVector2D textSize = canvas->K2_TextSize(font, playerConnected.c_str(), CG::CoreUObject::FVector2D(1.f, 1.f));

                float textHeight = textSize.Y;
                float xPos = 5.f;
                float yPos = (index * (textHeight)+5.f);

                CG::CoreUObject::FLinearColor color = Global::SkyRainbow(index, 1.f, 10.f);

                if (m_disableRainbow)
                {
                    color = m_color;
                }

                canvas->K2_DrawText(font, playerConnected.c_str(), { xPos, yPos }, CG::CoreUObject::FVector2D(1.0f, 1.0f), color, 0.f, { 0.f, 0.f, 0.f, 0.f }, { 0.f, 0.f }, false, false, false, { 0.f, 0.f, 0.f, 0.f });
                index += 1;
            }

            if (m_hexagons && playerController && playerController->GetPlayerCharacter() != nullptr)
            {
                std::wstring hexagons = (Global::ConvertUtf8ToUtf162(XOR("Hexagons: ")) + std::to_wstring(playerController->GetPlayerCharacter()->PlayerHexagonCount));
                CG::CoreUObject::FVector2D textSize = canvas->K2_TextSize(font, hexagons.c_str(), CG::CoreUObject::FVector2D(1.f, 1.f));

                float textHeight = textSize.Y;
                float xPos = 5.f;
                float yPos = (index * (textHeight)+5.f);

                CG::CoreUObject::FLinearColor color = Global::SkyRainbow(index, 1.f, 10.f);

                if (m_disableRainbow)
                {
                    color = m_color;
                }

                canvas->K2_DrawText(font, hexagons.c_str(), { xPos, yPos }, CG::CoreUObject::FVector2D(1.0f, 1.0f), color, 0.f, { 0.f, 0.f, 0.f, 0.f }, { 0.f, 0.f }, false, false, false, { 0.f, 0.f, 0.f, 0.f });
                index += 1;
            }

            if (m_clock)
            {
                int total_seconds = static_cast<int>(shooterGameState->DayTime);

                int hours = total_seconds / 3600; // Integer division to get the hours
                int remaining_seconds = total_seconds % 3600; // Modulo operation to get the remaining seconds
                int minutes = remaining_seconds / 60; // Integer division to get the minutes

                std::wstringstream wss;
                wss << Global::ConvertUtf8ToUtf162(XOR("Clock: ")) << std::setw(2) << std::setfill(L'0') << hours << XOR(L":") << std::setw(2) << std::setfill(L'0') << minutes;
                std::wstring serverTime = wss.str();


                CG::CoreUObject::FVector2D textSize = canvas->K2_TextSize(font, serverTime.c_str(), CG::CoreUObject::FVector2D(1.f, 1.f));

                float textHeight = textSize.Y;
                float xPos = 5.f;
                float yPos = (index * (textHeight)+5.f);

                CG::CoreUObject::FLinearColor color = Global::SkyRainbow(index, 1.f, 10.f);

                if (m_disableRainbow)
                {
                    color = m_color;
                }

                canvas->K2_DrawText(font, serverTime.c_str(), { xPos, yPos }, CG::CoreUObject::FVector2D(1.0f, 1.0f), color, 0.f, { 0.f, 0.f, 0.f, 0.f }, { 0.f, 0.f }, false, false, false, { 0.f, 0.f, 0.f, 0.f });
                index += 1;
            }

            if (m_tamedDinos)
            {
                std::wstring tamedDinos = (Global::ConvertUtf8ToUtf162(XOR("Tamed Dinos: ")) + std::to_wstring(shooterGameState->NumTamedDinos));

                CG::CoreUObject::FVector2D textSize = canvas->K2_TextSize(font, tamedDinos.c_str(), CG::CoreUObject::FVector2D(1.f, 1.f));

                float textHeight = textSize.Y;
                float xPos = 5.f;
                float yPos = (index * (textHeight)+5.f);

                CG::CoreUObject::FLinearColor color = Global::SkyRainbow(index, 1.f, 10.f);

                if (m_disableRainbow)
                {
                    color = m_color;
                }

                canvas->K2_DrawText(font, tamedDinos.c_str(), { xPos, yPos }, CG::CoreUObject::FVector2D(1.0f, 1.0f), color, 0.f, { 0.f, 0.f, 0.f, 0.f }, { 0.f, 0.f }, false, false, false, { 0.f, 0.f, 0.f, 0.f });
                index += 1;
            }

            if (playerController != nullptr && playerController->AcknowledgedPawn != nullptr && m_playersNearby)
            {
                auto statics = reinterpret_cast<CG::Engine::UGameplayStatics*>(playerController);
                int playerNearby = 0;

                CG::BasicTypes::TArray<CG::Engine::AActor*> outActors{};
                statics->STATIC_GetAllActorsOfClass(playerController, (CG::Engine::AActor*) CG::ShooterGame::AShooterCharacter::StaticClass(), &outActors);

                for (int y = 0; y < outActors.Count(); y++)
                {
                    auto currentActor = outActors[y];
                    {
                        if (currentActor == nullptr) continue;
                        if (currentActor->RootComponent == nullptr) continue;
                    }

                    auto player = static_cast<CG::ShooterGame::AShooterCharacter*>(currentActor);

                    if (player->TargetingTeam != playerController->TargetingTeam && player->bIsConnected && player->IsAlive())
                    {
                        playerNearby++;
                    }
                }

                std::wstring enemyPlayers = Global::ConvertUtf8ToUtf162(XOR("Enemy Players: ")) + std::to_wstring(
                    playerNearby);

                CG::CoreUObject::FVector2D textSize = canvas->K2_TextSize(font, enemyPlayers.c_str(), CG::CoreUObject::FVector2D(1.f, 1.f));

                float textHeight = textSize.Y;
                float xPos = 5.f;
                float yPos = (index * (textHeight)+5.f);

                CG::CoreUObject::FLinearColor color = Global::SkyRainbow(index, 1.f, 10.f);

                if (m_disableRainbow)
                {
                    color = m_color;
                }

                canvas->K2_DrawText(font, enemyPlayers.c_str(), { xPos, yPos }, CG::CoreUObject::FVector2D(1.0f, 1.0f), color, 0.f, { 0.f, 0.f, 0.f, 0.f }, { 0.f, 0.f }, false, false, false, { 0.f, 0.f, 0.f, 0.f });
                index += 1;
            }
        }

        int moduleIndex = 0;

        if (m_moduleList)
        {
            for (Base::Module* mod : Base::Manager.GetModules())
            {
                if (mod->GetEnabled())
                {
                    std::string name = mod->GetName(); // Get the module name as a std::string
                    int wideNameLength = MultiByteToWideChar(CP_UTF8, 0, name.c_str(), -1, nullptr, 0); // Get the length of the resulting wide string
                    std::wstring wideName(wideNameLength, 0); // Allocate a wstring with the correct length
                    MultiByteToWideChar(CP_UTF8, 0, name.c_str(), -1, &wideName[0], wideNameLength); // Convert the string to wide format
                    CG::BasicTypes::FString moduleName = wideName.c_str();

                    CG::CoreUObject::FVector2D textSize = canvas->K2_TextSize(font, moduleName, CG::CoreUObject::FVector2D(1.f, 1.f));
                    float textWidth = textSize.X;
                    float textHeight = textSize.Y;

                    float xPos = Global::ScreenWidth - textWidth - 5.f;
                    float yPos = (moduleIndex * (textHeight)+5.f);

                    CG::CoreUObject::FLinearColor moduleColor = Global::SkyRainbow(moduleIndex, 1.f, 5.f);

                    if (m_disableRainbow)
                    {
                        moduleColor = m_color;
                    }

                    canvas->K2_DrawText(font, moduleName, { xPos, yPos }, CG::CoreUObject::FVector2D(1.0f, 1.0f), moduleColor, 0.f, { 0.f, 0.f, 0.f, 0.f }, { 0.f, 0.f }, false, false, false, { 0.f, 0.f, 0.f, 0.f });

                    moduleIndex++;
                }
            }
        }
    }

    void ModuleHUD::LoadSettings(nlohmann::json config)
    {
        Module::LoadSettings(config);
        m_serverFps = GetCheckboxStateById(this->GetName(), 1, config);
        m_timer = GetCheckboxStateById(this->GetName(), 2, config);
        m_playerConnected = GetCheckboxStateById(this->GetName(), 3, config);
        m_tamedDinos = GetCheckboxStateById(this->GetName(), 4, config);
        m_servername = GetCheckboxStateById(this->GetName(), 5, config);
        m_mesh = GetCheckboxStateById(this->GetName(), 6, config);
        m_moduleList = GetCheckboxStateById(this->GetName(), 7, config);
        m_clock = GetCheckboxStateById(this->GetName(), 8, config);
        m_coordinates = GetCheckboxStateById(this->GetName(), 9, config);
        m_aimBone = GetCheckboxStateById(this->GetName(), 10, config);
        m_killCounterPlayers = GetCheckboxStateById(this->GetName(), 11, config);
        m_killCounterDinos = GetCheckboxStateById(this->GetName(), 12, config);
        m_deathCounter = GetCheckboxStateById(this->GetName(), 13, config);
        m_kd = GetCheckboxStateById(this->GetName(), 14, config);
        m_hexagons = GetCheckboxStateById(this->GetName(), 15, config);
        m_playersNearby = GetCheckboxStateById(this->GetName(), 16, config);
        m_disableRainbow = GetCheckboxStateById(this->GetName(), 17, config);
        m_reconnect = GetCheckboxStateById(this->GetName(), 18, config);

        m_color = GetColorValueById(this->GetName(), 1, config);

        // m_textScale = static_cast<int>(GetSliderCurrentValueById(this->GetName(), 1)) * 0.1f;
    }
}
