#include "pch.h"
#include "ModuleChams_Clean.h"

namespace Base
{
	// Static member initialization
	CG::Engine::UMaterialInstanceConstant* ModuleChams::s_chamsMaterial = nullptr;
	bool ModuleChams::s_materialInitialized = false;
	int ModuleChams::s_frameCounter = 0;

	void ModuleChams::SetupSettings()
	{
		// ===== CHAMS SETTINGS =====
		AddSetting(new CheckboxSetting(XOR("Show Players"), m_settings.players, 1));
		AddSetting(new CheckboxSetting(XOR("Show Offline Players"), m_settings.offlinePlayers, 2));
		AddSetting(new CheckboxSetting(XOR("Show Friendly Players"), m_settings.friendlyPlayers, 3));
		AddSetting(new CheckboxSetting(XOR("Show Tamed Dinos"), m_settings.tamedDinos, 4));
		AddSetting(new CheckboxSetting(XOR("Show Wild Dinos"), m_settings.wildDinos, 5));
		AddSetting(new CheckboxSetting(XOR("Show Turrets"), m_settings.turrets, 6));
		AddSetting(new CheckboxSetting(XOR("Show Beds"), m_settings.beds, 7));
		AddSetting(new CheckboxSetting(XOR("Show Generators"), m_settings.generators, 8));
		AddSetting(new CheckboxSetting(XOR("Show C4 Charges"), m_settings.c4Charges, 9));
		AddSetting(new CheckboxSetting(XOR("Show Tek Beds"), m_settings.tekBeds, 10));
		AddSetting(new CheckboxSetting(XOR("Show Heavy Turrets"), m_settings.heavyTurrets, 11));
		AddSetting(new CheckboxSetting(XOR("Show Tek Turrets"), m_settings.tekTurrets, 12));

		// ===== COLOR SETTINGS =====
		AddSetting(new ColorsSetting(XOR("Player Color"), XOR("#ff0000"), 1));
		AddSetting(new ColorsSetting(XOR("Offline Player Color"), XOR("#800000"), 2));
		AddSetting(new ColorsSetting(XOR("Friendly Player Color"), XOR("#00f2eb"), 3));
		AddSetting(new ColorsSetting(XOR("Tamed Dino Color"), XOR("#ff8500"), 4));
		AddSetting(new ColorsSetting(XOR("Wild Dino Color"), XOR("#cccc00"), 5));
		AddSetting(new ColorsSetting(XOR("Turret Color"), XOR("#d400ff"), 6));
		AddSetting(new ColorsSetting(XOR("Bed Color"), XOR("#0000ff"), 7));
		AddSetting(new ColorsSetting(XOR("Generator Color"), XOR("#0026ff"), 8));
		AddSetting(new ColorsSetting(XOR("C4 Charge Color"), XOR("#7800b8"), 9));
		AddSetting(new ColorsSetting(XOR("Tek Bed Color"), XOR("#00ffff"), 10));
		AddSetting(new ColorsSetting(XOR("Heavy Turret Color"), XOR("#ff0080"), 11));
		AddSetting(new ColorsSetting(XOR("Tek Turret Color"), XOR("#8000ff"), 12));
	}

	void ModuleChams::LoadSettings(nlohmann::json config)
	{
		Module::LoadSettings(config);
		LoadChamsSettings(config);
		LoadColorSettings(config);
	}

	void ModuleChams::OnPostRender(CG::ShooterGame::UShooterGameViewportClient* viewport, CG::Engine::UCanvas* canvas, 
								   CG::Engine::UWorld* world, CG::Engine::UFont* font)
	{
		if (!IsEnabled()) return;

		// Performance optimization: Process every 3rd frame instead of random
		if (++s_frameCounter % PROCESS_FREQUENCY != 0) return;

		RenderContext ctx;
		if (!InitializeRenderContext(ctx, world)) return;

		// Initialize actor handlers if not done yet
		if (m_actorHandlers.empty())
			InitializeActorHandlers();

		ProcessActors(ctx);
	}

	void ModuleChams::OnDisable(CG::Engine::UWorld* world)
	{
		if (!world) return;
		CleanupActors(world);
	}

	bool ModuleChams::InitializeRenderContext(RenderContext& ctx, CG::Engine::UWorld* world)
	{
		if (!world || !world->OwningGameInstance || !world->OwningGameInstance->LocalPlayers.Count())
			return false;

		auto localPlayer = world->OwningGameInstance->LocalPlayers[0];
		if (!localPlayer || !localPlayer->PlayerController)
			return false;

		// Use FNameCache instead of StaticClass()
		if (!FNameCache::IsA(localPlayer->PlayerController, FNameCache::Actors::ShooterPlayerController))
			return false;

		ctx.playerController = static_cast<CG::ShooterGame::AShooterPlayerController*>(localPlayer->PlayerController);
		if (!ctx.playerController || !ctx.playerController->PlayerCameraManager)
			return false;

		ctx.selfPlayer = ctx.playerController->GetPlayerCharacter();
		if (!ctx.selfPlayer)
			return false;

		ctx.world = world;
		ctx.chamsMaterial = GetChamsMaterial();
		if (!ctx.chamsMaterial)
			return false;

		return true;
	}

	void ModuleChams::ProcessActors(const RenderContext& ctx)
	{
		auto persistentLevel = ctx.world->PersistentLevel;
		if (!persistentLevel || !persistentLevel->ActorsList.Count())
			return;

		auto actorArray = persistentLevel->ActorsList;

		for (int i = 0; i < actorArray.Count(); ++i)
		{
			auto actor = actorArray[i];
			if (!ShouldProcessActor(actor, ctx))
				continue;

			// Use FNameCache for efficient type checking
			if (FNameCache::IsA(actor, FNameCache::Actors::ShooterCharacter))
			{
				ProcessPlayers(ctx, actor);
			}
			else if (FNameCache::IsA(actor, FNameCache::Actors::PrimalDinoCharacter))
			{
				ProcessDinos(ctx, actor);
			}
			else if (FNameCache::IsA(actor, FNameCache::Actors::PrimalStructureTurret))
			{
				ProcessTurrets(ctx, actor);
			}
			else if (FNameCache::IsA(actor, FNameCache::Actors::PrimalStructureBed))
			{
				ProcessBeds(ctx, actor);
			}
			else if (FNameCache::IsA(actor, FNameCache::Containers::TekGenerator))
			{
				ProcessGenerators(ctx, actor);
			}
			else
			{
				// Check for other specific types using ComparisonIndex
				auto comparisonIndex = actor->Name.ComparisonIndex;
				auto handlerIt = m_actorHandlers.find(comparisonIndex);
				if (handlerIt != m_actorHandlers.end())
				{
					handlerIt->second(ctx, actor);
				}
			}
		}
	}

	void ModuleChams::ProcessPlayers(const RenderContext& ctx, CG::Engine::AActor* actor)
	{
		auto player = static_cast<CG::ShooterGame::AShooterCharacter*>(actor);
		if (!player || !player->Mesh || !player->PlayerName.IsValid())
			return;

		// Skip self
		if (player->LinkedPlayerDataID == ctx.selfPlayer->LinkedPlayerDataID)
		{
			// Clean up self materials
			CleanupCharacterMaterials(player);
			return;
		}

		bool isFriendly = ctx.selfPlayer->IsPrimalCharFriendly(player);
		bool isConnected = player->bIsConnected;

		// Apply chams based on settings
		if (isConnected && !isFriendly && m_settings.players)
		{
			ApplyCharacterChams(player, m_colors.players, ctx);
		}
		else if (!isConnected && !isFriendly && m_settings.offlinePlayers)
		{
			ApplyCharacterChams(player, m_colors.offlinePlayers, ctx);
		}
		else if (isConnected && isFriendly && m_settings.friendlyPlayers)
		{
			ApplyCharacterChams(player, m_colors.friendlyPlayers, ctx);
		}
		else
		{
			// Clean up materials if not applying chams
			CleanupCharacterMaterials(player);
		}
	}

	void ModuleChams::ProcessDinos(const RenderContext& ctx, CG::Engine::AActor* actor)
	{
		auto dino = static_cast<CG::ShooterGame::APrimalDinoCharacter*>(actor);
		if (!dino || !dino->Mesh || !dino->IsAlive())
		{
			if (dino && dino->Mesh)
				CleanupCharacterMaterials(dino);
			return;
		}

		bool isTamed = dino->BPIsTamed();
		bool isEnemy = IsEnemyActor(dino, ctx);

		// Apply chams based on settings
		if (isTamed && isEnemy && m_settings.tamedDinos)
		{
			ApplyCharacterChams(dino, m_colors.tamedDinos, ctx);
		}
		else if (!isTamed && m_settings.wildDinos)
		{
			ApplyCharacterChams(dino, m_colors.wildDinos, ctx);
		}
		else
		{
			// Clean up materials if not applying chams
			CleanupCharacterMaterials(dino);
		}
	}

	void ModuleChams::ProcessTurrets(const RenderContext& ctx, CG::Engine::AActor* actor)
	{
		if (!m_settings.turrets && !m_settings.heavyTurrets && !m_settings.tekTurrets)
		{
			// Clean up if no turret chams enabled
			auto turret = static_cast<CG::ShooterGame::APrimalStructureTurret*>(actor);
			CleanupStructureMaterials(turret);
			return;
		}

		auto turret = static_cast<CG::ShooterGame::APrimalStructureTurret*>(actor);
		if (!IsEnemyActor(turret, ctx))
		{
			CleanupStructureMaterials(turret);
			return;
		}

		// Check specific turret types using FNameCache
		if (m_settings.heavyTurrets && FNameCache::IsA(actor, FNameCache::Structures::HeavyTurret))
		{
			auto heavyTurret = static_cast<CG::StructureTurretBaseBP_BaseHeavy::AStructureTurretBaseBP_BaseHeavy_C*>(actor);
			if (heavyTurret && heavyTurret->SkeletalMesh1)
				ApplySkeletalChams(heavyTurret->SkeletalMesh1, m_colors.heavyTurrets, ctx);
		}
		else if (m_settings.tekTurrets && FNameCache::IsA(actor, FNameCache::Structures::TekTurret))
		{
			auto tekTurret = static_cast<CG::StructureTurretTek::AStructureTurretTek_C*>(actor);
			if (tekTurret && tekTurret->SkeletalMesh1)
				ApplySkeletalChams(tekTurret->SkeletalMesh1, m_colors.tekTurrets, ctx);
		}
		else if (m_settings.turrets)
		{
			ApplyStructureChams(turret, m_colors.turrets, ctx);
		}
		else
		{
			CleanupStructureMaterials(turret);
		}
	}

	void ModuleChams::ProcessBeds(const RenderContext& ctx, CG::Engine::AActor* actor)
	{
		if (!m_settings.beds && !m_settings.tekBeds)
		{
			auto bed = static_cast<CG::ShooterGame::APrimalStructureBed*>(actor);
			CleanupStructureMaterials(bed);
			return;
		}

		auto bed = static_cast<CG::ShooterGame::APrimalStructureBed*>(actor);
		if (!IsEnemyActor(bed, ctx))
		{
			CleanupStructureMaterials(bed);
			return;
		}

		// Check for Tek Bed using FNameCache (when available)
		if (m_settings.tekBeds && FNameCache::IsA(actor, FNameCache::Structures::TekBed))
		{
			auto tekBed = static_cast<CG::ATekBed_C*>(actor);
			if (tekBed && tekBed->SkeletalMesh1)
				ApplySkeletalChams(tekBed->SkeletalMesh1, m_colors.tekBeds, ctx);
		}
		else if (m_settings.beds)
		{
			ApplyStructureChams(bed, m_colors.beds, ctx);
		}
		else
		{
			CleanupStructureMaterials(bed);
		}
	}

	void ModuleChams::ProcessGenerators(const RenderContext& ctx, CG::Engine::AActor* actor)
	{
		if (!m_settings.generators)
		{
			auto generator = static_cast<CG::StorageBox_TekGenerator::AStorageBox_TekGenerator_C*>(actor);
			if (generator && generator->SkeletalMesh1)
				CleanupSkeletalMaterials(generator->SkeletalMesh1);
			return;
		}

		auto generator = static_cast<CG::StorageBox_TekGenerator::AStorageBox_TekGenerator_C*>(actor);
		if (!IsEnemyActor(generator, ctx))
		{
			if (generator && generator->SkeletalMesh1)
				CleanupSkeletalMaterials(generator->SkeletalMesh1);
			return;
		}

		if (generator && generator->SkeletalMesh1)
			ApplySkeletalChams(generator->SkeletalMesh1, m_colors.generators, ctx);
	}

	void ModuleChams::ProcessC4Charges(const RenderContext& ctx, CG::Engine::AActor* actor)
	{
		if (!m_settings.c4Charges)
			return;

		// C4 charges are typically enemy by default, so apply chams
		auto c4Charge = static_cast<CG::ShooterGame::APrimalStructure*>(actor);
		ApplyStructureChams(c4Charge, m_colors.c4Charges, ctx);
	}
}
