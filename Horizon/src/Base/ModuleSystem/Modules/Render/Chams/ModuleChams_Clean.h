#pragma once

#include "pch.h"
#include <unordered_map>
#include <functional>

namespace Base
{
	/**
	 * @brief High-performance Chams module with clean architecture and FNameCache integration
	 * 
	 * Features:
	 * - Efficient FNameCache-based actor filtering
	 * - Clean material management system
	 * - Modular chams application with type-specific handlers
	 * - Optimized rendering pipeline
	 * - Complete settings implementation
	 */
	class ModuleChams final : public Module
	{
	public:
		ModuleChams(const std::string& name, const std::string& description, const int key_code, 
					const Category category, const std::string& icon)
			: Module(name, description, key_code, category, icon) {}

		void SetupSettings() override;
		void LoadSettings(nlohmann::json config) override;
		void OnPostRender(CG::ShooterGame::UShooterGameViewportClient* viewport, CG::Engine::UCanvas* canvas, 
						  CG::Engine::UWorld* world, CG::Engine::UFont* font) override;
		void OnDisable(CG::Engine::UWorld* world) override;

	private:
		// ===== CONFIGURATION STRUCTURES =====

		struct ChamsSettings
		{
			bool players = false;
			bool offlinePlayers = false;
			bool friendlyPlayers = false;
			bool tamedDinos = false;
			bool wildDinos = false;
			bool turrets = false;
			bool beds = false;
			bool generators = false;
			bool c4Charges = false;
			bool tekBeds = false;
			bool heavyTurrets = false;
			bool tekTurrets = false;
		};

		struct ChamsColors
		{
			CG::CoreUObject::FLinearColor players = {1.0f, 0.0f, 0.0f, 1.0f};
			CG::CoreUObject::FLinearColor offlinePlayers = {0.5f, 0.0f, 0.0f, 1.0f};
			CG::CoreUObject::FLinearColor friendlyPlayers = {0.0f, 0.95f, 0.92f, 1.0f};
			CG::CoreUObject::FLinearColor tamedDinos = {1.0f, 0.52f, 0.0f, 1.0f};
			CG::CoreUObject::FLinearColor wildDinos = {0.8f, 0.8f, 0.0f, 1.0f};
			CG::CoreUObject::FLinearColor turrets = {0.83f, 0.0f, 1.0f, 1.0f};
			CG::CoreUObject::FLinearColor beds = {0.0f, 0.0f, 1.0f, 1.0f};
			CG::CoreUObject::FLinearColor generators = {0.0f, 0.15f, 1.0f, 1.0f};
			CG::CoreUObject::FLinearColor c4Charges = {0.47f, 0.0f, 0.72f, 1.0f};
			CG::CoreUObject::FLinearColor tekBeds = {0.0f, 1.0f, 1.0f, 1.0f};
			CG::CoreUObject::FLinearColor heavyTurrets = {1.0f, 0.0f, 0.5f, 1.0f};
			CG::CoreUObject::FLinearColor tekTurrets = {0.5f, 0.0f, 1.0f, 1.0f};
		};

		// ===== SETTINGS INSTANCES =====
		ChamsSettings m_settings;
		ChamsColors m_colors;

		// ===== RENDERING CONTEXT =====
		struct RenderContext
		{
			CG::ShooterGame::AShooterPlayerController* playerController;
			CG::ShooterGame::AShooterCharacter* selfPlayer;
			CG::Engine::UWorld* world;
			CG::Engine::UMaterialInstanceConstant* chamsMaterial;
		};

		// ===== CORE METHODS =====
		bool InitializeRenderContext(RenderContext& ctx, CG::Engine::UWorld* world);
		void ProcessActors(const RenderContext& ctx);
		void CleanupActors(CG::Engine::UWorld* world);

		// ===== ACTOR PROCESSORS =====
		void ProcessPlayers(const RenderContext& ctx, CG::Engine::AActor* actor);
		void ProcessDinos(const RenderContext& ctx, CG::Engine::AActor* actor);
		void ProcessTurrets(const RenderContext& ctx, CG::Engine::AActor* actor);
		void ProcessBeds(const RenderContext& ctx, CG::Engine::AActor* actor);
		void ProcessGenerators(const RenderContext& ctx, CG::Engine::AActor* actor);
		void ProcessC4Charges(const RenderContext& ctx, CG::Engine::AActor* actor);

		// ===== MATERIAL APPLICATION =====
		void ApplyCharacterChams(CG::ShooterGame::APrimalCharacter* target, 
								 const CG::CoreUObject::FLinearColor& color, const RenderContext& ctx);
		void ApplyStructureChams(CG::ShooterGame::APrimalStructure* target, 
								 const CG::CoreUObject::FLinearColor& color, const RenderContext& ctx);
		void ApplySkeletalChams(CG::Engine::USkeletalMeshComponent* target, 
								const CG::CoreUObject::FLinearColor& color, const RenderContext& ctx);

		// ===== MATERIAL CLEANUP =====
		void CleanupCharacterMaterials(CG::ShooterGame::APrimalCharacter* character);
		void CleanupStructureMaterials(CG::ShooterGame::APrimalStructure* structure);
		void CleanupSkeletalMaterials(CG::Engine::USkeletalMeshComponent* skeletal);

		// ===== UTILITIES =====
		CG::Engine::UMaterialInstanceConstant* GetChamsMaterial();
		bool IsEnemyActor(CG::Engine::AActor* actor, const RenderContext& ctx) const;
		bool ShouldProcessActor(CG::Engine::AActor* actor, const RenderContext& ctx) const;

		// ===== SETTINGS HELPERS =====
		void LoadChamsSettings(const nlohmann::json& config);
		void LoadColorSettings(const nlohmann::json& config);

		// ===== CACHED MATERIAL =====
		static CG::Engine::UMaterialInstanceConstant* s_chamsMaterial;
		static bool s_materialInitialized;

		// ===== ACTOR TYPE HANDLERS =====
		using ActorHandler = std::function<void(const RenderContext&, CG::Engine::AActor*)>;
		std::unordered_map<int, ActorHandler> m_actorHandlers;
		void InitializeActorHandlers();

		// ===== PERFORMANCE OPTIMIZATION =====
		static constexpr int PROCESS_FREQUENCY = 3; // Process every 3rd frame instead of random
		static int s_frameCounter;
	};
}
