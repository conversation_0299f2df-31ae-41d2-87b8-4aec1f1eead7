#pragma once

#include "pch.h"
#include <mutex>

#include "CRT/CRT.h"
#include "ModuleSystem/ModuleBase/Settings/Config/Config.h"

namespace Global
{
	inline std::wstring ConvertUtf8ToUtf162(const std::string& str)
	{
		std::wstring_convert<std::codecvt_utf8_utf16<wchar_t>, wchar_t> converter;
		return converter.from_bytes(str);
	}

	inline std::string ConvertUtf16ToUtf82(const std::wstring& wstr)
	{
		std::wstring_convert<std::codecvt_utf8_utf16<wchar_t>, wchar_t> converter;
		return converter.to_bytes(wstr);
	}

	inline FILE* File;
	inline HMODULE HModule;

	inline CG::ShooterGame::UPrimalInventoryComponent* LastInventory = nullptr;

	inline int ImprintMultiply = 1;

	inline bool EnablePrivate = true;
	inline bool InitialLoad = false;

	inline int ScreenWidth = 1920;
	inline int ScreenHeight = 1080;
	inline int ServerFps = 20;

	inline int KillCounterPlayers = 0;
	inline int KillCounterDinos = 0;
	inline int DeathCounter = 0;
	inline std::string LastestPlayerId = "None";
	inline int TimeBeforeReconnect = 0;

	inline std::unordered_map<int, bool> KeyStates;

	inline std::wstring ActiveBone = Global::ConvertUtf8ToUtf162(XOR("Head"));

	inline bool IsConfigAccessible() {
		std::ifstream file(Base::GetConfigPath());

		if (file.is_open()) {
			file.close();
			return true;
		}
		else
		{
			return false;
		}
	}

	inline CG::ShooterGame::UVictoryCore* GetVictoryCore()
	{
		auto world = *CG::Engine::UWorld::GWorld;
		while (world == nullptr) { world = *CG::Engine::UWorld::GWorld; }

		return reinterpret_cast<CG::ShooterGame::UVictoryCore*>(world);
	}

	inline float GetDistance2D(const CG::CoreUObject::FVector2D one, const CG::CoreUObject::FVector2D two)
	{
		return CRT::CustomSqrtf(((two.X - one.X) * (two.X - one.X) + (two.Y - one.Y) * (two.Y - one.Y)));
	}

	inline float GetDistance3D(float x1, float y1, float z1, float x2, float y2, float z2)
	{
		float distance = CRT::CustomSqrtf(static_cast<float>(pow(x2 - x1, 2) + pow(y2 - y1, 2) + pow(z2 - z1, 2) * 1.0));

		return distance;
	}

	template <typename T>
	inline T* GetDefaultObject()
	{
		// Check if T is a subclass of UObject at compile time

		/*auto victory_core = GetVictoryCore();
		if (!victory_core)
		{
			std::cout << "GetVictoryCore is null" << std::endl;
			return nullptr;
		}*/

		CG::CoreUObject::UClass* classPtr = T::StaticClass();
		if (!classPtr)
		{
			// Get the mangled class name
			std::string typeName = typeid(T).name();
			// std::cout << "StaticClass returned null for type: " << typeName << "  ->  " << classPtr->GetFullName() << std::endl;
			return nullptr;
		}

		// Assuming GetFullName() is a method of UObject or inherited by T
		std::string className = classPtr->GetFullName();

		//std::cout << "Class Full Name: " << className << std::endl;

		//auto result = victory_core->STATIC_GetClassDefaultObject(classPtr);
		// std::cout << "GetClassDefaultObject returned null for class " << className << std::endl;
		CG::CoreUObject::UClass* type = T::StaticClass();

		for (int32_t i = 0; i < CG::CoreUObject::UObject::GetGlobalObjects().Count(); ++i)
		{
			auto object = CG::CoreUObject::UObject::GetGlobalObjects().GetByIndex(i);

			if (!object) continue;

			if (object->IsA(type) && object->GetFullName().find("Default") != 0)
			{
				//std::cout << "Found default good " << className << std::endl;
				return static_cast<T*>(object);
			}
		}

		return nullptr;
	}

	template <typename T>
	inline T* GetDefaultObjectExact()
	{
		// Check if T is a subclass of UObject at compile time

		auto victory_core = GetVictoryCore();
		if (!victory_core)
		{
			// std::cout << "GetVictoryCore is null" << std::endl;
			return nullptr;
		}

		CG::CoreUObject::UClass* classPtr = T::StaticClass();
		if (!classPtr)
		{
			// Get the mangled class name
			std::string typeName = typeid(T).name();
			// std::cout << "StaticClass returned null for type: " << typeName << "  ->  " << classPtr->GetFullName() << std::endl;
			return nullptr;
		}

		// Assuming GetFullName() is a method of UObject or inherited by T
		std::string className = classPtr->GetFullName();

		//std::cout << "Class Full Name: " << className << std::endl;

		auto result = victory_core->STATIC_GetClassDefaultObject(classPtr);
		if (!result)
		{
			// std::cout << "GetClassDefaultObject returned null for class " << className << std::endl;
			CG::CoreUObject::UClass* type = T::StaticClass();

			for (int32_t i = 0; i < CG::CoreUObject::UObject::GetGlobalObjects().Count(); ++i)
			{
				auto object = CG::CoreUObject::UObject::GetGlobalObjects().GetByIndex(i);

				if (!object) continue;

				if (object == type && object->GetFullName().find("Default") != 0)
				{
					//std::cout << "Found default good " << className << std::endl;
					return static_cast<T*>(object);
				}
			}

			return nullptr;
		}

		return static_cast<T*>(result);
	}

	inline CG::CoreUObject::UClass* GetDefaultObjectByClassExact(CG::CoreUObject::UClass* clz)
	{
		for (int32_t i = 0; i < CG::CoreUObject::UObject::GetGlobalObjects().Count(); ++i)
		{
			auto object = CG::CoreUObject::UObject::GetGlobalObjects().GetByIndex(i);

			if (!object) continue;

			if (object->Class == clz)
			{
				// Store the result in the cache. Lock is required here as well.
				auto insertedObject = static_cast<CG::CoreUObject::UClass*>(object);

				//std::cout << "Found default good222 " << object->GetFullName() << std::endl;

				return insertedObject;
			}
		}

		return nullptr;
	}

	inline CG::CoreUObject::UClass* GetDefaultObjectByClass(CG::CoreUObject::UClass* clz)
	{
		static std::unordered_map<CG::CoreUObject::UClass*, CG::CoreUObject::UClass*> cache;
		static std::mutex cacheMutex;

		{
			// Lock the mutex for the scope of the cache lookup
			std::lock_guard<std::mutex> lock(cacheMutex);

			// Check if the class is already in the cache
			auto it = cache.find(clz);
			if (it != cache.end())
			{
				return it->second;
			}
		}

		for (int32_t i = 0; i < CG::CoreUObject::UObject::GetGlobalObjects().Count(); ++i)
		{
			auto object = CG::CoreUObject::UObject::GetGlobalObjects().GetByIndex(i);

			if (!object) continue;

			if (object->IsA(clz) && object->GetFullName().find("Default") != 0)
			{
				// Store the result in the cache. Lock is required here as well.
				std::lock_guard<std::mutex> lock(cacheMutex);
				auto insertedObject = static_cast<CG::CoreUObject::UClass*>(object);
				cache[clz] = insertedObject;

				//std::cout << "Found default good222 " << object->GetFullName() << std::endl;

				return insertedObject;
			}
		}

		// If not found, store nullptr in the cache
		std::lock_guard<std::mutex> lock(cacheMutex);
		cache[clz] = nullptr;
		return nullptr;
	}


	inline CG::Foliage::AInstancedFoliageActor* GetInstancedFoliageActor()
	{
		return reinterpret_cast<CG::Foliage::AInstancedFoliageActor*>(CG::Foliage::AInstancedFoliageActor::StaticClass());
	}

	inline CG::Engine::UKismetMathLibrary* GetKismetMathLibrary()
	{
		return reinterpret_cast<CG::Engine::UKismetMathLibrary*>(CG::Engine::UKismetMathLibrary::StaticClass());
	}

	inline CG::Engine::UKismetStringLibrary* GetKismetStringLibrary()
	{
		return reinterpret_cast<CG::Engine::UKismetStringLibrary*>(CG::Engine::UKismetStringLibrary::StaticClass());
	}

	inline CG::Engine::UKismetSystemLibrary* GetKismetSystemLibrary()
	{
		return reinterpret_cast<CG::Engine::UKismetSystemLibrary*>(CG::Engine::UKismetSystemLibrary::StaticClass());
	}

	inline CG::Engine::UGameplayStatics* GetGameplayStatics()
	{
		return reinterpret_cast<CG::Engine::UGameplayStatics*>(CG::Engine::UGameplayStatics::StaticClass());
	}

	inline CG::ShooterGame::UShooterGameInstance* GetGameInstance(CG::Engine::UWorld* world)
	{
		if (world == nullptr) return nullptr;

		auto gameInstance = static_cast<CG::ShooterGame::UShooterGameInstance*>(world->OwningGameInstance);
		if (gameInstance == nullptr || (!gameInstance->LocalPlayers.Count())) return nullptr;

		return gameInstance;
	}

	inline CG::ShooterGame::UShooterLocalPlayer* GetLocalPlayer(CG::Engine::UWorld* world)
	{
		if (world == nullptr) return nullptr;

		auto gameInstance = GetGameInstance(world);
		if (gameInstance == nullptr) return nullptr;

		auto localPlayer = static_cast<CG::ShooterGame::UShooterLocalPlayer*>(gameInstance->LocalPlayers[0]);
		if (localPlayer == nullptr || localPlayer->PlayerController == nullptr) return nullptr;

		return localPlayer;
	}

	inline CG::ShooterGame::AShooterPlayerController* GetPlayerController(CG::Engine::UWorld* world)
	{
		if (!world) return nullptr;

		auto PCarray = GetVictoryCore()->STATIC_GetAllLocalPlayerControllers(world);
		if (PCarray.Count() == 0) return nullptr;

		auto playerController = PCarray[0];

		return playerController;
	}

	inline float NormalizeAngle(float angle)
	{
		while (angle > 180.0f)
			angle -= 360.0f;
		while (angle < -180.0f)
			angle += 360.0f;
		return angle;
	}

	inline CG::ShooterGame::UPrimalInventoryComponent* GetDrop()
	{
		CG::Engine::UWorld* world = *CG::Engine::UWorld::GWorld;
		CG::ShooterGame::APrimalStructureItemContainer_SupplyCrate* drop = nullptr;

		while (world == nullptr)
		{
			world = *CG::Engine::UWorld::GWorld;
		}

		CG::ShooterGame::AShooterPlayerController* playerController = Global::GetPlayerController(world);
		if (playerController == nullptr) return nullptr;

		auto statics = reinterpret_cast<CG::Engine::UGameplayStatics*>(playerController);

		if (playerController != nullptr && playerController->AcknowledgedPawn != nullptr)
		{
			CG::BasicTypes::TArray<CG::Engine::AActor*> outActors{};
			statics->STATIC_GetAllActorsOfClass(playerController, (CG::Engine::AActor*) CG::ShooterGame::APrimalStructureItemContainer_SupplyCrate::StaticClass(), &outActors);

			for (int y = 0; y < outActors.Count(); y++)
			{
				auto currentActor = outActors[y];
				{
					if (currentActor == nullptr) continue;
					if (currentActor->RootComponent == nullptr) continue;
				}

				drop = static_cast<CG::ShooterGame::APrimalStructureItemContainer_SupplyCrate*>(currentActor);

				return drop->MyInventoryComponent;
			}
		}

		return nullptr;
	}

	inline CG::ShooterGame::UPrimalInventoryComponent* GetActorInv()
	{
		CG::Engine::UWorld* world = *CG::Engine::UWorld::GWorld;

		while (world == nullptr)
		{
			world = *CG::Engine::UWorld::GWorld;
		}

		CG::ShooterGame::AShooterPlayerController* playerController = Global::GetPlayerController(world);
		if (playerController == nullptr) return nullptr;

		if (playerController->GetPlayerCharacter() == nullptr) return nullptr;

		auto inv = GetDrop();

		if (inv != nullptr)
		{
			return inv;
		}

		auto statics = reinterpret_cast<CG::Engine::UGameplayStatics*>(playerController);

		if (playerController != nullptr && playerController->AcknowledgedPawn != nullptr)
		{
			CG::BasicTypes::TArray<CG::Engine::AActor*> outActors2{};
			statics->STATIC_GetAllActorsOfClass(playerController, (CG::Engine::AActor*)CG::ShooterGame::APrimalStructureItemContainer::StaticClass(), &outActors2);

			for (int y = 0; y < outActors2.Count(); y++)
			{
				auto currentActor = outActors2[y];
				{
					if (currentActor == nullptr) continue;
					if (currentActor->RootComponent == nullptr) continue;
				}

				auto drop = static_cast<CG::ShooterGame::APrimalStructureItemContainer*>(currentActor);

				if (drop->MyInventoryComponent == nullptr) continue;
				if (playerController->GetPlayerCharacter()->MyInventoryComponent == drop->MyInventoryComponent) continue;

				drop->MyInventoryComponent->MaxInventoryAccessDistance = 99999999999.f;
				drop->MyInventoryComponent->MaxRemoteInventoryViewingDistance = 99999999999.f;
				drop->MyInventoryComponent->bReceivingArkInventoryItems = false;
				drop->MyInventoryComponent->bReceivingInventoryItems = false;
				drop->MyInventoryComponent->bReceivingEquippedItems = false;
				drop->MyInventoryComponent->bAllowRemoteInventory = true;

				return drop->MyInventoryComponent;
			}
		}

		return nullptr;
	}

	inline CG::Engine::AActor* GetClosestRidableDino(CG::Engine::UWorld* world)
	{
		CG::ShooterGame::AShooterPlayerController* playerController = Global::GetPlayerController(world);
		if (playerController == nullptr) {
			return nullptr;
		}

		CG::Engine::ULevel* persistentLevel = world->PersistentLevel;
		if (persistentLevel == nullptr) {
			return nullptr;
		}
		CG::BasicTypes::TArray<CG::Engine::AActor*> actorArray = persistentLevel->ActorsList;

		CG::ShooterGame::AShooterCharacter* selfPlayer = playerController->GetPlayerCharacter();

		if (!selfPlayer) return nullptr;

		for (int i = 0; i < actorArray.Count(); i++)
		{
			const auto currentActor = actorArray[i];
			{
				if (currentActor == nullptr) continue;
				if (currentActor->RootComponent == nullptr) continue;
			}

			if (currentActor->IsA(CG::Dino_Character_BP::ADino_Character_BP_C::StaticClass()))
			{
				const auto dino = static_cast<CG::Dino_Character_BP::ADino_Character_BP_C*>(currentActor);

				if (dino == nullptr) continue;
				if (selfPlayer == nullptr) continue;

				if (dino->TargetingTeam != selfPlayer->TargetingTeam || !dino->IsAlive() || dino->bIsMounted || selfPlayer->bIsRiding || selfPlayer->CanMountOnMe(dino)) continue;

				const CG::CoreUObject::FVector playerLocation = selfPlayer->K2_GetActorLocation();
				const CG::CoreUObject::FVector dinoLocation = dino->K2_GetActorLocation();

				const float distance = Global::GetDistance3D(playerLocation.X, playerLocation.Y, playerLocation.Z, dinoLocation.X, dinoLocation.Y, dinoLocation.Z);

				if (distance < 1500)
				{
					return dino;
				}
			}
		}

		return nullptr;
	}

	inline bool IsInScreen(CG::CoreUObject::FVector2D pos, int ScreenWidth, int ScreenHeight)
	{
		if (((pos.X <= 0 or pos.X > ScreenWidth) and (pos.Y <= 0 or pos.Y > ScreenHeight)) or ((pos.X <= 0 or pos.X > ScreenWidth) or (pos.Y <= 0 or pos.Y > ScreenHeight)))
		{
			return FALSE;
		}
		else
		{
			return TRUE;
		}
	}

	template<typename T>
	FORCEINLINE static CG::BasicTypes::TWeakObjectPtr<T> ToWeak(T* object)
	{
		return CG::BasicTypes::TWeakObjectPtr<T>(object);
	};

	inline CG::CoreUObject::FLinearColor SkyRainbow(int moduleIndex, float bright, float speed)
	{
		double time = std::ceil(std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count() + static_cast<long>(moduleIndex * 209)) / (5.0 * speed);
		float hue = static_cast<float>(std::fmod(time, 360.0) / 360.0);

		float r = 0.0f, g = 0.0f, b = 0.0f;
		if (hue < 0.16666667f) {
			r = 1.0f;
			g = hue * 6.0f;
		}
		else if (hue < 0.33333333f) {
			r = (0.33333333f - hue) * 6.0f;
			g = 1.0f;
		}
		else if (hue < 0.5f) {
			g = 1.0f;
			b = (hue - 0.33333333f) * 6.0f;
		}
		else if (hue < 0.66666667f) {
			g = (0.66666667f - hue) * 6.0f;
			b = 1.0f;
		}
		else if (hue < 0.83333333f) {
			r = (hue - 0.66666667f) * 6.0f;
			b = 1.0f;
		}
		else {
			r = 1.0f;
			b = (1.0f - hue) * 6.0f;
		}

		return CG::CoreUObject::FLinearColor(r * bright, g * bright, b * bright, 1.0f);
	}

	inline CG::Dino_Character_BP::ADino_Character_BP_C* GetDummyDino(CG::Engine::UWorld* world)
	{
		CG::ShooterGame::AShooterPlayerController* playerController = Global::GetPlayerController(world);
		if (playerController == nullptr) {
			printf("error 0");
			return nullptr;
		}

		CG::ShooterGame::AShooterCharacter* selfPlayer = playerController->GetPlayerCharacter();
		if (!selfPlayer) return nullptr;

		CG::BasicTypes::TArray<CG::Engine::AActor*> actorArray = selfPlayer->GetLevel()->ActorsList;
		std::cout << actorArray.Count() << std::endl;

		printf("pass 3");

		for (int i = 0; i < actorArray.Count(); i++)
		{
			const auto currentActor = actorArray[i];
			{
				if (currentActor == nullptr) continue;
				if (currentActor->RootComponent == nullptr) continue;
			}

			if (currentActor->IsA(CG::Dino_Character_BP::ADino_Character_BP_C::StaticClass()))
			{
				const auto dino = static_cast<CG::Dino_Character_BP::ADino_Character_BP_C*>(currentActor);

				if (dino == nullptr) continue;
				if (selfPlayer == nullptr) continue;

				if (!dino->IsAlive()) continue;

				return dino;
			}
		}

		return nullptr;
	}

	inline std::wstring ConvertCharPtrToWstring(const char* charPtr) {
		int strLength = static_cast<int>(strlen(charPtr));
		int requiredSize = MultiByteToWideChar(CP_UTF8, 0, charPtr, strLength, NULL, 0);
		std::wstring wstr(requiredSize, 0);
		MultiByteToWideChar(CP_UTF8, 0, charPtr, strLength, &wstr[0], requiredSize);
		return wstr;
	}
}
