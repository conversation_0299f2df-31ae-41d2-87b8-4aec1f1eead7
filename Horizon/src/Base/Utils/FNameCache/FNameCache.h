#pragma once

#include "pch.h"

namespace Base
{
    /**
     * @brief Fast FName cache system for efficient actor type checking
     *
     * This class provides cached FName ComparisonIndex values to replace slow
     * StaticClass() comparisons with fast integer comparisons.
     *
     * Usage:
     *   if (actor->Name.ComparisonIndex == FNameCache::Actors::PrimalDinoCharacter)
     *   {
     *       // Handle dino
     *   }
     */
    class FNameCache
    {
    public:
        /**
         * @brief Initialize all FName caches - call this once during startup
         */
        static void Initialize();

        /**
         * @brief Check if the cache has been initialized
         */
        static bool IsInitialized() { return s_initialized; }

        // Actor type FNames
        struct Actors
        {
            static int PrimalDinoCharacter;
            static int ShooterCharacter;
            static int PrimalCharacter;
            static int PrimalStructure;
            static int PrimalStructureItemContainer;
            static int PrimalStructureBed;
            static int PrimalStructureTurret;
            static int PrimalStructureItemContainerSupplyCrate;
            static int DroppedItemEgg;
            static int DroppedItemGeneric;
            static int ExplorerChestBase;
        };

        // Structure type FNames
        struct Structures
        {
            // Foundations
            static int MetalFoundation;
            static int TekFoundation;

            // Walls
            static int MetalWall;
            static int TekWall;

            // Ceilings
            static int MetalCeiling;
            static int TekCeiling;

            // Pillars
            static int MetalPillar;
            static int TekPillar;

            // Doors
            static int MetalDoubleDoor;
            static int TekDoubleDoor;

            // Gates
            static int MetalGates;
            static int TekGates;

            // Ladders
            static int MetalLadder;
            static int TekLadder;

            // Special structures
            static int CliffPlatform;
            static int Vacuums;
            static int TekCellarDoor;
            static int TekCellarDoorXL;

            // Triangle foundations
            static int MetalTriangle;
            static int TekTriangle;
        };

        // Storage container FNames
        struct Containers
        {
            static int StorageBoxSmall;
            static int StorageBoxLarge;
            static int StorageBoxHuge; // Vault
            static int DedicatedStorage;
            static int TekGenerator;
            static int ElectricGenerator;
            static int CryoFridge;
            static int DeathItemCache;
            static int AmmoContainer;
        };

        // Item/Resource FNames
        struct Items
        {
            static int SupplyCrate;
            static int DroppedItem;
        };

        // Utility functions for common checks
        static bool IsActor(int comparisonIndex, int cachedIndex)
        {
            return comparisonIndex == cachedIndex;
        }

        /**
         * @brief Inheritance-aware IsA function using ComparisonIndex
         *
         * This function replicates UObject::IsA() behavior but uses ComparisonIndex
         * instead of UClass pointers for better performance and reliability.
         *
         * @param actor The actor to check
         * @param targetClassComparisonIndex The ComparisonIndex of the target class
         * @return true if actor is of the target class or inherits from it
         */
        static bool IsA(CG::Engine::AActor* actor, int targetClassComparisonIndex);

        static bool IsAnyStructure(int comparisonIndex)
        {
            return comparisonIndex == Structures::MetalFoundation ||
                   comparisonIndex == Structures::TekFoundation ||
                   comparisonIndex == Structures::MetalWall ||
                   comparisonIndex == Structures::TekWall ||
                   comparisonIndex == Structures::MetalCeiling ||
                   comparisonIndex == Structures::TekCeiling ||
                   comparisonIndex == Structures::MetalPillar ||
                   comparisonIndex == Structures::TekPillar ||
                   comparisonIndex == Structures::MetalDoubleDoor ||
                   comparisonIndex == Structures::TekDoubleDoor ||
                   comparisonIndex == Structures::MetalGates ||
                   comparisonIndex == Structures::TekGates ||
                   comparisonIndex == Structures::MetalLadder ||
                   comparisonIndex == Structures::TekLadder ||
                   comparisonIndex == Structures::CliffPlatform ||
                   comparisonIndex == Structures::Vacuums ||
                   comparisonIndex == Structures::TekCellarDoor ||
                   comparisonIndex == Structures::TekCellarDoorXL ||
                   comparisonIndex == Structures::MetalTriangle ||
                   comparisonIndex == Structures::TekTriangle;
        }

        static bool IsAnyContainer(int comparisonIndex)
        {
            return comparisonIndex == Containers::StorageBoxSmall ||
                   comparisonIndex == Containers::StorageBoxLarge ||
                   comparisonIndex == Containers::StorageBoxHuge ||
                   comparisonIndex == Containers::DedicatedStorage ||
                   comparisonIndex == Containers::TekGenerator ||
                   comparisonIndex == Containers::ElectricGenerator ||
                   comparisonIndex == Containers::CryoFridge ||
                   comparisonIndex == Containers::DeathItemCache ||
                   comparisonIndex == Containers::AmmoContainer;
        }

    private:
        static bool s_initialized;

        /**
         * @brief Helper function to safely convert string to FName ComparisonIndex
         */
        static int GetComparisonIndex(const wchar_t* className);
    };
}
