#include "pch.h"
#include "FNameCache.h"

namespace Base
{
    // Static member initialization
    bool FNameCache::s_initialized = false;

    // Actor type FNames
    int FNameCache::Actors::PrimalDinoCharacter = 0;
    int FNameCache::Actors::ShooterCharacter = 0;
    int FNameCache::Actors::PrimalCharacter = 0;
    int FNameCache::Actors::PrimalStructure = 0;
    int FNameCache::Actors::PrimalStructureItemContainer = 0;
    int FNameCache::Actors::PrimalStructureBed = 0;
    int FNameCache::Actors::PrimalStructureTurret = 0;
    int FNameCache::Actors::PrimalStructureItemContainerSupplyCrate = 0;
    int FNameCache::Actors::DroppedItemEgg = 0;
    int FNameCache::Actors::DroppedItemGeneric = 0;
    int FNameCache::Actors::ExplorerChestBase = 0;

    // Structure type FNames
    int FNameCache::Structures::MetalFoundation = 0;
    int FNameCache::Structures::TekFoundation = 0;
    int FNameCache::Structures::MetalWall = 0;
    int FNameCache::Structures::TekWall = 0;
    int FNameCache::Structures::MetalCeiling = 0;
    int FNameCache::Structures::TekCeiling = 0;
    int FNameCache::Structures::MetalPillar = 0;
    int FNameCache::Structures::TekPillar = 0;
    int FNameCache::Structures::MetalDoubleDoor = 0;
    int FNameCache::Structures::TekDoubleDoor = 0;
    int FNameCache::Structures::MetalGates = 0;
    int FNameCache::Structures::TekGates = 0;
    int FNameCache::Structures::MetalLadder = 0;
    int FNameCache::Structures::TekLadder = 0;
    int FNameCache::Structures::CliffPlatform = 0;
    int FNameCache::Structures::Vacuums = 0;
    int FNameCache::Structures::TekCellarDoor = 0;
    int FNameCache::Structures::TekCellarDoorXL = 0;
    int FNameCache::Structures::MetalTriangle = 0;
    int FNameCache::Structures::TekTriangle = 0;

    // Container type FNames
    int FNameCache::Containers::StorageBoxSmall = 0;
    int FNameCache::Containers::StorageBoxLarge = 0;
    int FNameCache::Containers::StorageBoxHuge = 0;
    int FNameCache::Containers::DedicatedStorage = 0;
    int FNameCache::Containers::TekGenerator = 0;
    int FNameCache::Containers::ElectricGenerator = 0;
    int FNameCache::Containers::CryoFridge = 0;
    int FNameCache::Containers::DeathItemCache = 0;
    int FNameCache::Containers::AmmoContainer = 0;

    // Item type FNames
    int FNameCache::Items::SupplyCrate = 0;
    int FNameCache::Items::DroppedItem = 0;

    void FNameCache::Initialize()
    {
        if (s_initialized)
            return;

        try
        {
            // Initialize Actor FNames
            Actors::PrimalDinoCharacter = GetComparisonIndex(XOR(L"PrimalDinoCharacter"));
            Actors::ShooterCharacter = GetComparisonIndex(XOR(L"ShooterCharacter"));
            Actors::PrimalCharacter = GetComparisonIndex(XOR(L"PrimalCharacter"));
            Actors::PrimalStructure = GetComparisonIndex(XOR(L"PrimalStructure"));
            Actors::PrimalStructureItemContainer = GetComparisonIndex(XOR(L"PrimalStructureItemContainer"));
            Actors::PrimalStructureBed = GetComparisonIndex(XOR(L"PrimalStructureBed"));
            Actors::PrimalStructureTurret = GetComparisonIndex(XOR(L"PrimalStructureTurret"));
            Actors::PrimalStructureItemContainerSupplyCrate = GetComparisonIndex(XOR(L"PrimalStructureItemContainer_SupplyCrate"));
            Actors::DroppedItemEgg = GetComparisonIndex(XOR(L"DroppedItemEgg"));
            Actors::DroppedItemGeneric = GetComparisonIndex(XOR(L"DroppedItemGeneric_C"));
            Actors::ExplorerChestBase = GetComparisonIndex(XOR(L"ExplorerChest_Base_C"));

            // Initialize Structure FNames (using exact names from your original code)
            Structures::MetalFoundation = GetComparisonIndex(XOR(L"Floor_Metal_C"));
            Structures::TekFoundation = GetComparisonIndex(XOR(L"Floor_Tek_C"));
            Structures::MetalWall = GetComparisonIndex(XOR(L"Wall_Metal_C"));
            Structures::TekWall = GetComparisonIndex(XOR(L"Wall_Metal_C")); // Note: original had same as metal
            Structures::MetalCeiling = GetComparisonIndex(XOR(L"Ceiling_Metal_C"));
            Structures::TekCeiling = GetComparisonIndex(XOR(L"Ceiling_Tek_C"));
            Structures::MetalPillar = GetComparisonIndex(XOR(L"Pillar_Metal_C"));
            Structures::TekPillar = GetComparisonIndex(XOR(L"Pillar_Tek_C"));
            Structures::MetalDoubleDoor = GetComparisonIndex(XOR(L"BP_DoubleDoorframe_Metal_C"));
            Structures::TekDoubleDoor = GetComparisonIndex(XOR(L"BP_DoubleDoorframe_Tek_C"));
            Structures::MetalGates = GetComparisonIndex(XOR(L"GateFrame_Metal_C"));
            Structures::TekGates = GetComparisonIndex(XOR(L"GateFrame_Tek_C"));
            Structures::MetalLadder = GetComparisonIndex(XOR(L"Ladder_Metal_C"));
            Structures::TekLadder = GetComparisonIndex(XOR(L"Ladder_Tek_C"));
            Structures::CliffPlatform = GetComparisonIndex(XOR(L"Metal_Platform_BP_Small_C"));
            Structures::Vacuums = GetComparisonIndex(XOR(L"Underwater_Base_SM_Full_C"));
            Structures::TekCellarDoor = GetComparisonIndex(XOR(L"Ceiling_Door_Giant_Tek_C"));
            Structures::TekCellarDoorXL = GetComparisonIndex(XOR(L"BP_Ceiling_Door_XL_Tek_C"));
            Structures::MetalTriangle = GetComparisonIndex(XOR(L"BP_TriFoundation_Metal_C"));
            Structures::TekTriangle = GetComparisonIndex(XOR(L"BP_TriFoundation_Tek_C"));

            // Initialize Container FNames
            Containers::StorageBoxSmall = GetComparisonIndex(XOR(L"StorageBox_Small_C"));
            Containers::StorageBoxLarge = GetComparisonIndex(XOR(L"StorageBox_Large_C"));
            Containers::StorageBoxHuge = GetComparisonIndex(XOR(L"StorageBox_Huge_C"));
            Containers::DedicatedStorage = GetComparisonIndex(XOR(L"BP_DedicatedStorage_C"));
            Containers::TekGenerator = GetComparisonIndex(XOR(L"StorageBox_TekGenerator_C"));
            Containers::ElectricGenerator = GetComparisonIndex(XOR(L"ElectricGenerator_C"));
            Containers::CryoFridge = GetComparisonIndex(XOR(L"CryoFridge_C"));
            Containers::DeathItemCache = GetComparisonIndex(XOR(L"DeathItemCache_C"));
            Containers::AmmoContainer = GetComparisonIndex(XOR(L"StructureAmmoContainer_C"));

            // Initialize Item FNames
            Items::SupplyCrate = GetComparisonIndex(XOR(L"SupplyCrate_C"));
            Items::DroppedItem = GetComparisonIndex(XOR(L"DroppedItem_C"));

            s_initialized = true;

#ifdef DEBUG
            printf(XOR("FNameCache initialized successfully\n"));
#endif
        }
        catch (...)
        {
#ifdef DEBUG
            printf(XOR("Failed to initialize FNameCache\n"));
#endif
        }
    }

    bool FNameCache::IsA(CG::Engine::AActor* actor, int targetClassComparisonIndex)
    {
        if (!actor || !s_initialized)
            return false;

        try
        {
            // Get the actor's class
            auto actorClass = actor->Class;
            if (!actorClass)
                return false;

            // Walk up the inheritance hierarchy
            for (auto currentClass = actorClass; currentClass; currentClass = static_cast<CG::CoreUObject::UClass*>(currentClass->SuperField))
            {
                if (!currentClass->Name.IsValid())
                    continue;

                // Check if current class matches target
                if (currentClass->Name.ComparisonIndex == targetClassComparisonIndex)
                    return true;
            }

            return false;
        }
        catch (...)
        {
#ifdef DEBUG
            printf(XOR("Exception in FNameCache::IsA\n"));
#endif
            return false;
        }
    }

    int FNameCache::GetComparisonIndex(const wchar_t* className)
    {
        try
        {
            auto kismetStringLibrary = Global::GetKismetStringLibrary();
            if (!kismetStringLibrary)
            {
#ifdef DEBUG
                printf(XOR("KismetStringLibrary is null\n"));
#endif
                return 0;
            }

            auto fname = kismetStringLibrary->STATIC_Conv_StringToName(className);
            return fname.ComparisonIndex;
        }
        catch (...)
        {
#ifdef DEBUG
            printf(XOR("Failed to get ComparisonIndex for class: %ls\n"), className);
#endif
            return 0;
        }
    }
}
