#pragma once

/**
 * @file FNameCacheExample.h
 * @brief Example usage of the FNameCache system for efficient actor type checking
 * 
 * This file demonstrates how to use the FNameCache system to replace slow
 * StaticClass() comparisons with fast integer comparisons.
 * 
 * BEFORE (slow):
 *   if (actor->IsA(CG::ShooterGame::APrimalDinoCharacter::StaticClass()))
 *   {
 *       // Handle dino
 *   }
 * 
 * AFTER (fast):
 *   if (FNameCache::IsActor(actor->Name.ComparisonIndex, FNameCache::Actors::Primal<PERSON>inoCharacter))
 *   {
 *       // Handle dino
 *   }
 * 
 * BENEFITS:
 * - Much faster performance (integer comparison vs class loading)
 * - No risk of StaticClass() returning null when classes aren't loaded
 * - Cleaner, more readable code
 * - Centralized management of all class names
 * 
 * USAGE EXAMPLES:
 * 
 * 1. Check for specific actor types:
 *    if (FNameCache::IsActor(actor->Name.ComparisonIndex, FNameCache::Actors::ShooterCharacter))
 *    {
 *        auto player = static_cast<CG::ShooterGame::AShooter<PERSON>haracter*>(actor);
 *        // Handle player
 *    }
 * 
 * 2. Check for any structure:
 *    if (FNameCache::IsAnyStructure(actor->Name.ComparisonIndex))
 *    {
 *        // This actor is a structure we want to filter out
 *        continue;
 *    }
 * 
 * 3. Check for any container:
 *    if (FNameCache::IsAnyContainer(actor->Name.ComparisonIndex))
 *    {
 *        // This actor is a storage container
 *        auto container = static_cast<CG::ShooterGame::APrimalStructureItemContainer*>(actor);
 *        // Handle container
 *    }
 * 
 * 4. Check for specific containers:
 *    if (FNameCache::IsActor(actor->Name.ComparisonIndex, FNameCache::Containers::DedicatedStorage))
 *    {
 *        auto dediStorage = static_cast<CG::BP_DedicatedStorage::ABP_DedicatedStorage_C*>(actor);
 *        // Handle dedicated storage
 *    }
 * 
 * AVAILABLE CATEGORIES:
 * 
 * FNameCache::Actors::
 *   - PrimalDinoCharacter
 *   - ShooterCharacter
 *   - PrimalCharacter
 *   - PrimalStructure
 *   - PrimalStructureItemContainer
 *   - PrimalStructureBed
 *   - PrimalStructureTurret
 *   - PrimalStructureItemContainerSupplyCrate
 *   - DroppedItemEgg
 *   - DroppedItemGeneric
 *   - ExplorerChestBase
 * 
 * FNameCache::Structures::
 *   - MetalFoundation, TekFoundation
 *   - MetalWall, TekWall
 *   - MetalCeiling, TekCeiling
 *   - MetalPillar, TekPillar
 *   - MetalDoubleDoor, TekDoubleDoor
 *   - MetalGates, TekGates
 *   - MetalLadder, TekLadder
 *   - CliffPlatform, Vacuums
 *   - TekCellarDoor, TekCellarDoorXL
 *   - MetalTriangle, TekTriangle
 * 
 * FNameCache::Containers::
 *   - StorageBoxSmall, StorageBoxLarge, StorageBoxHuge
 *   - DedicatedStorage
 *   - TekGenerator, ElectricGenerator
 *   - CryoFridge
 *   - DeathItemCache
 *   - AmmoContainer
 * 
 * FNameCache::Items::
 *   - SupplyCrate
 *   - DroppedItem
 * 
 * HELPER FUNCTIONS:
 * 
 * FNameCache::IsActor(comparisonIndex, cachedIndex) - Check specific actor type
 * FNameCache::IsAnyStructure(comparisonIndex) - Check if any structure type
 * FNameCache::IsAnyContainer(comparisonIndex) - Check if any container type
 * FNameCache::IsInitialized() - Check if cache is ready to use
 * 
 * INITIALIZATION:
 * The FNameCache is automatically initialized during startup in Base.cpp.
 * You don't need to initialize it manually.
 * 
 * ADDING NEW TYPES:
 * To add new actor types:
 * 1. Add static int declaration to appropriate struct in FNameCache.h
 * 2. Add static member initialization in FNameCache.cpp
 * 3. Add GetComparisonIndex() call in Initialize() function
 * 4. Update helper functions like IsAnyStructure() if needed
 */
